#!/usr/bin/env python3
"""
Base validator class for XML data validation.

This module provides a base class for validating extracted XML data
with common validation methods and error handling.
"""

import logging
import re
from abc import ABC, abstractmethod
from datetime import datetime


class ValidationError(Exception):
    """Custom exception for validation errors."""

    pass


class BaseValidator(ABC):
    """
    Base class for XML data validators.

    This class provides common functionality for validating extracted data
    including format validation, business rule validation, and error handling.
    """

    def __init__(self, logger=None):
        """
        Initialize the base validator.

        Args:
            logger (logging.Logger): Logger instance. If None, creates a new one.
        """
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        self.errors = []
        self.warnings = []

    def clear_errors(self):
        """Clear all validation errors and warnings."""
        self.errors = []
        self.warnings = []

    def add_error(self, message, field=None):
        """
        Add a validation error.

        Args:
            message (str): Error message.
            field (str): Field name that caused the error.
        """
        error = {"message": message, "field": field, "type": "error"}
        self.errors.append(error)
        self.logger.error(
            f"Validation error: {message}" + (f" (field: {field})" if field else "")
        )

    def add_warning(self, message, field=None):
        """
        Add a validation warning.

        Args:
            message (str): Warning message.
            field (str): Field name that caused the warning.
        """
        warning = {"message": message, "field": field, "type": "warning"}
        self.warnings.append(warning)
        self.logger.warning(
            f"Validation warning: {message}" + (f" (field: {field})" if field else "")
        )

    def has_errors(self):
        """
        Check if there are any validation errors.

        Returns:
            bool: True if there are errors, False otherwise.
        """
        return len(self.errors) > 0

    def has_warnings(self):
        """
        Check if there are any validation warnings.

        Returns:
            bool: True if there are warnings, False otherwise.
        """
        return len(self.warnings) > 0

    def get_errors(self):
        """
        Get all validation errors.

        Returns:
            list: List of error dictionaries.
        """
        return self.errors.copy()

    def get_warnings(self):
        """
        Get all validation warnings.

        Returns:
            list: List of warning dictionaries.
        """
        return self.warnings.copy()

    def validate_required_field(self, value, field_name):
        """
        Validate that a required field has a value.

        Args:
            value: Field value to validate.
            field_name (str): Name of the field.

        Returns:
            bool: True if valid, False otherwise.
        """
        if value is None or (isinstance(value, str) and not value.strip()):
            self.add_error(
                f"Required field '{field_name}' is missing or empty", field_name
            )
            return False
        return True

    def validate_string_length(
        self, value, field_name, min_length=None, max_length=None
    ):
        """
        Validate string length.

        Args:
            value (str): String value to validate.
            field_name (str): Name of the field.
            min_length (int): Minimum allowed length.
            max_length (int): Maximum allowed length.

        Returns:
            bool: True if valid, False otherwise.
        """
        if value is None:
            return True  # Let validate_required_field handle None values

        if not isinstance(value, str):
            value = str(value)

        length = len(value)

        if min_length is not None and length < min_length:
            self.add_error(
                f"Field '{field_name}' is too short (minimum {min_length} characters)",
                field_name,
            )
            return False

        if max_length is not None and length > max_length:
            self.add_error(
                f"Field '{field_name}' is too long (maximum {max_length} characters)",
                field_name,
            )
            return False

        return True

    def validate_pattern(self, value, field_name, pattern, pattern_description=None):
        """
        Validate that a value matches a regex pattern.

        Args:
            value (str): Value to validate.
            field_name (str): Name of the field.
            pattern (str): Regex pattern.
            pattern_description (str): Human-readable description of the pattern.

        Returns:
            bool: True if valid, False otherwise.
        """
        if value is None:
            return True  # Let validate_required_field handle None values

        if not isinstance(value, str):
            value = str(value)

        if not re.match(pattern, value):
            desc = pattern_description or f"pattern {pattern}"
            self.add_error(f"Field '{field_name}' does not match {desc}", field_name)
            return False

        return True

    def validate_numeric_range(self, value, field_name, min_value=None, max_value=None):
        """
        Validate that a numeric value is within a specified range.

        Args:
            value: Numeric value to validate.
            field_name (str): Name of the field.
            min_value: Minimum allowed value.
            max_value: Maximum allowed value.

        Returns:
            bool: True if valid, False otherwise.
        """
        if value is None:
            return True  # Let validate_required_field handle None values

        try:
            num_value = float(value)
        except (ValueError, TypeError):
            self.add_error(f"Field '{field_name}' is not a valid number", field_name)
            return False

        if min_value is not None and num_value < min_value:
            self.add_error(
                f"Field '{field_name}' is below minimum value {min_value}", field_name
            )
            return False

        if max_value is not None and num_value > max_value:
            self.add_error(
                f"Field '{field_name}' is above maximum value {max_value}", field_name
            )
            return False

        return True

    def validate_choice(self, value, field_name, choices):
        """
        Validate that a value is one of the allowed choices.

        Args:
            value: Value to validate.
            field_name (str): Name of the field.
            choices (list): List of allowed values.

        Returns:
            bool: True if valid, False otherwise.
        """
        if value is None:
            return True  # Let validate_required_field handle None values

        if value not in choices:
            self.add_error(
                f"Field '{field_name}' has invalid value '{value}'. Allowed values: {choices}",
                field_name,
            )
            return False

        return True

    def validate_awb_number(self, awb_number, field_name="awb_number"):
        """
        Validate AWB number format - flexible validation for real-world data.

        Args:
            awb_number (str): AWB number to validate.
            field_name (str): Name of the field.

        Returns:
            bool: True if valid, False otherwise.
        """
        if not self.validate_required_field(awb_number, field_name):
            return False

        # More flexible AWB format validation:
        # - Standard format: 3 digits, hyphen, 7-8 digits (XXX-XXXXXXX or XXX-XXXXXXXX)
        # - Allow variations in digit count for real-world compatibility
        # - Must have at least 3 digits, hyphen, and 6+ digits
        pattern = r"^\d{3}-\d{6,8}$"

        if not re.match(pattern, awb_number):
            self.add_warning(
                f"AWB number '{awb_number}' has non-standard format (expected XXX-XXXXXXXX), but will be accepted",
                field_name,
            )
            # Still accept it - just warn, don't fail

        return True

    def validate_airport_code(self, code, field_name):
        """
        Validate airport code format (3 letters).

        Args:
            code (str): Airport code to validate.
            field_name (str): Name of the field.

        Returns:
            bool: True if valid, False otherwise.
        """
        if not self.validate_required_field(code, field_name):
            return False

        pattern = r"^[A-Z]{3}$"
        return self.validate_pattern(code, field_name, pattern, "3-letter airport code")

    @abstractmethod
    def validate(self, data):
        """
        Validate extracted data. Must be implemented by subclasses.

        Args:
            data (dict): Data to validate.

        Returns:
            bool: True if valid, False otherwise.
        """
        pass
