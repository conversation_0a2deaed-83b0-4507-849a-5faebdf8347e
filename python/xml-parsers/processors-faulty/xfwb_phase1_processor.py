#!/usr/bin/env python3
"""
XFWB Phase 1 Processor for the Integrated Cargo XML Parser System.

This processor handles the first phase of the two-stage processing system:
- Parses XFWB (Master Waybill XML) files
- Establishes authoritative AWB data with duplicate prevention using SHA-256 hashing
- Creates master waybill declarations with expected totals
- Logs processing activity for audit trails
"""

import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any

from database.integrated_operations import IntegratedDatabaseOperations
from database.fallback_operations import FallbackEnhancedOperations
from extractors.xfwb_extractor import XFWBExtractor


class XFWBPhase1Processor:
    """
    XFWB Phase 1 processor for master waybill declarations.

    Implements the first phase of the integrated cargo processing system:
    1. Parse XFWB XML files
    2. Generate and check AWB hashes for duplicate prevention
    3. Create/update master waybill records with expected totals
    4. Log processing activity
    """

    def __init__(self, db_connection, db_cursor, branch_id=1, user_id=1, logger=None):
        """
        Initialize XFWB Phase 1 processor.

        Args:
            db_connection: Database connection object
            db_cursor: Database cursor object
            branch_id (int): Current branch ID
            user_id (int): Current user ID
            logger: Logger instance
        """
        self.db_ops = FallbackEnhancedOperations(
            db_connection, db_cursor, branch_id, user_id, logger
        )
        self.extractor = XFWBExtractor(logger)
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        self.branch_id = branch_id
        self.user_id = user_id

    def process_xfwb_file(self, file_path: str) -> Dict[str, Any]:
        """
        Process a single XFWB XML file with enhanced business logic.

        Args:
            file_path (str): Path to XFWB XML file

        Returns:
            dict: Processing result with statistics and status
        """
        start_time = time.time()
        file_name = file_path.split('/')[-1]

        # Start enhanced processing log
        log_id = self.db_ops.start_processing_log(file_name, 'XFWB')

        result = {
            'success': False,
            'file_name': file_name,
            'log_id': log_id,
            'message_id': None,
            'awb_count': 0,
            'processed_awbs': [],
            'skipped_awbs': [],
            'errors': [],
            'warnings': [],
            'processing_time_ms': 0
        }

        try:
            self.logger.info(f"Starting enhanced XFWB processing for file: {file_path}")

            # Check for file-level duplicates
            import os
            file_hash = self.db_ops.generate_file_hash(file_path)
            existing_file = self.db_ops.check_file_duplicate(file_hash)

            if existing_file:
                self.db_ops.record_duplicate_attempt(file_hash)
                self.db_ops.mark_processing_duplicate(log_id)

                result['success'] = False
                result['message'] = 'Duplicate file detected'
                result['duplicate_info'] = existing_file
                self.logger.info(f"Duplicate XFWB file detected: {file_name}")
                return result

            # Record file as being processed
            file_size = os.path.getsize(file_path)
            self.db_ops.record_file_processed(file_path, file_hash, 'XFWB', file_size)

            # Extract data from XFWB file
            extracted_data = self.extractor.extract_from_file(file_path)

            if not extracted_data or 'success' not in extracted_data or not extracted_data['success']:
                error_msg = f"Failed to extract data from XFWB file: {file_path}"
                result['errors'].append(error_msg)
                self.logger.error(error_msg)
                self.db_ops.complete_processing_log(log_id, False, error_message=error_msg)
                return result

            # Get message ID for logging
            result['message_id'] = extracted_data.get('message_id', 'UNKNOWN')

            # Process the master waybill
            awb_data = extracted_data.get('master_waybill', {})
            if not awb_data:
                error_msg = "No master waybill data found in XFWB file"
                result['errors'].append(error_msg)
                self.logger.error(error_msg)
                return result

            # Process the AWB
            awb_result = self.process_single_awb(awb_data, extracted_data)

            if awb_result['success']:
                result['processed_awbs'].append(awb_result['awb_number'])
                result['awb_count'] = 1
                result['warnings'].extend(awb_result.get('warnings', []))
            else:
                result['skipped_awbs'].append(awb_result['awb_number'])
                result['errors'].extend(awb_result.get('errors', []))

            # Calculate processing time
            processing_time_ms = int((time.time() - start_time) * 1000)
            result['processing_time_ms'] = processing_time_ms

            # Complete enhanced processing log
            processing_summary = {
                'awb_number': result.get('processed_awbs', [None])[0] if result['processed_awbs'] else None,
                'awbs_processed': result['awb_count'],
                'enrichment_type': 'XFWB_DECLARATION'
            }

            # Commit transaction if successful
            if result['awb_count'] > 0:
                self.db_ops.commit_transaction()
                result['success'] = True
                self.db_ops.complete_processing_log(
                    log_id, True, result['awb_count'], 0, None, processing_summary
                )
                self.logger.info(f"Successfully processed XFWB file: {result['file_name']}")
            else:
                self.db_ops.rollback_transaction()
                self.db_ops.complete_processing_log(
                    log_id, False, 0, 0, "No AWBs processed", processing_summary
                )
                self.logger.warning(f"No AWBs processed from file: {result['file_name']}")

        except Exception as e:
            # Rollback transaction on error
            self.db_ops.rollback_transaction()
            error_msg = f"Error processing XFWB file {file_path}: {str(e)}"
            result['errors'].append(error_msg)
            result['processing_time_ms'] = int((time.time() - start_time) * 1000)
            self.logger.error(error_msg, exc_info=True)

            # Complete processing log with error
            self.db_ops.complete_processing_log(log_id, False, error_message=error_msg)

        return result

    def process_single_awb(self, awb_data: Dict[str, Any],
                          full_extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single AWB from XFWB data.

        Args:
            awb_data (dict): AWB data extracted from XFWB
            full_extracted_data (dict): Complete extracted data for context

        Returns:
            dict: Processing result for this AWB
        """
        result = {
            'success': False,
            'awb_number': awb_data.get('awb_number', 'UNKNOWN'),
            'action': None,
            'errors': [],
            'warnings': []
        }

        try:
            awb_number = awb_data['awb_number']
            self.logger.info(f"Processing AWB: {awb_number}")

            # Step 1: Generate AWB content hash for enhanced duplicate prevention
            content_hash = self.db_ops.generate_awb_content_hash(awb_data)

            # Step 2: Check if this exact AWB content has been processed before
            # For XFWB files, we should UPDATE existing records, not skip them
            # Only skip if the content is truly identical (same hash)
            if self.db_ops.check_awb_content_duplicate(awb_number, content_hash):
                # Check if this is truly identical content or if we should update
                existing_awb = self.db_ops.get_master_waybill_by_number(awb_number)
                if existing_awb:
                    # XFWB should enrich/update existing AWB records, not skip them
                    self.logger.info(f"AWB {awb_number} exists with identical content - will enrich existing record")
                    # Continue processing to update the record
                else:
                    result['action'] = 'SKIPPED_DUPLICATE'
                    result['warnings'].append(f"AWB {awb_number} skipped - identical content already processed")
                    self.logger.info(f"Skipping duplicate AWB content: {awb_number}")
                    return result

            # Step 3: Record AWB as processed
            self.db_ops.record_awb_processed(
                awb_number, content_hash,
                full_extracted_data.get('manifest_id', 'XFWB'),
                result.get('file_name', 'unknown')
            )

            # Step 4: Prepare enhanced AWB data for database storage
            processed_awb_data = self.prepare_enhanced_awb_data(awb_data, full_extracted_data)

            # Step 5: Create or update master waybill using enhanced operations
            awb_number, was_created = self.db_ops.create_or_update_master_waybill_enhanced(
                processed_awb_data, 'XFWB'
            )

            if was_created:
                result['action'] = 'CREATED'
                self.logger.info(f"Created new master waybill from XFWB: {awb_number}")
            else:
                result['action'] = 'ENRICHED'
                self.logger.info(f"Enriched existing master waybill with XFWB data: {awb_number}")

            result['success'] = True

        except Exception as e:
            error_msg = f"Error processing AWB {result['awb_number']}: {str(e)}"
            result['errors'].append(error_msg)
            self.logger.error(error_msg, exc_info=True)

        return result

    def prepare_enhanced_awb_data(self, awb_data: Dict[str, Any],
                                 full_extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare enhanced AWB data for database storage with new business logic.

        Args:
            awb_data (dict): Raw AWB data from extractor
            full_extracted_data (dict): Complete extracted data

        Returns:
            dict: Enhanced AWB data for database
        """
        # Get current branch code for in-transit logic
        current_branch_code = self.db_ops.get_branch_code(self.branch_id)
        destination_code = awb_data.get('destination_airport', '')

        # Determine if this is an in-transit shipment
        is_in_transit = False
        if destination_code != current_branch_code:
            # Check if destination has a branch
            dest_branch_id = self.db_ops.check_destination_branch_exists(destination_code)
            is_in_transit = dest_branch_id is not None

        # Extract party information
        shipper_info = awb_data.get('shipper', {})
        consignee_info = awb_data.get('consignee', {})

        return {
            'awb_number': awb_data['awb_number'],
            'type_code': awb_data.get('type_code', '740'),
            'manifest_id': full_extracted_data.get('manifest_id'),
            'origin_airport': awb_data['origin_airport'],
            'destination_airport': awb_data['destination_airport'],
            'total_pieces': awb_data.get('total_pieces', 0),
            'total_weight': awb_data.get('total_weight', 0),
            'weight_unit': awb_data.get('weight_unit', 'KGM'),
            'gross_volume': awb_data.get('gross_volume'),
            'volume_unit': awb_data.get('volume_unit'),
            'summary_description': awb_data.get('summary_description'),
            'special_handling_code': awb_data.get('special_handling_code'),
            'special_handling_codes': awb_data.get('special_handling_codes', []),
            'goods_descriptions': awb_data.get('goods_descriptions', []),
            'shipper_code': shipper_info.get('code') if shipper_info else None,
            'consignee_code': consignee_info.get('code') if consignee_info else None,
            'currency_code': awb_data.get('currency_code'),
            'prepaid_collect_indicator': awb_data.get('prepaid_collect_indicator'),
            'in_transit': is_in_transit,
            'xml_data': full_extracted_data
        }

    def prepare_awb_data(self, awb_data: Dict[str, Any],
                        full_extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare AWB data for database storage.

        Args:
            awb_data (dict): Raw AWB data from extractor
            full_extracted_data (dict): Complete extracted data

        Returns:
            dict: Prepared AWB data for database
        """
        # Get current branch code for in-transit logic
        current_branch_code = self.db_ops.get_branch_code(self.branch_id)
        destination_code = awb_data.get('destination_airport', '')

        # Determine if this is an in-transit shipment
        is_in_transit = False
        if destination_code != current_branch_code:
            # Check if destination has a branch
            dest_branch_id = self.db_ops.check_destination_branch_exists(destination_code)
            is_in_transit = dest_branch_id is not None

        return {
            'awb_number': awb_data['awb_number'],
            'type_code': awb_data.get('type_code', '740'),
            'manifest_id': full_extracted_data.get('manifest_id'),
            'origin_airport': awb_data['origin_airport'],
            'destination_airport': awb_data['destination_airport'],
            'total_pieces': awb_data.get('total_pieces', 0),
            'total_weight': awb_data.get('total_weight', 0),
            'weight_unit': awb_data.get('weight_unit', 'KGM'),
            'gross_volume': awb_data.get('gross_volume'),
            'volume_unit': awb_data.get('volume_unit'),
            'summary_description': awb_data.get('summary_description'),
            'special_handling_code': awb_data.get('special_handling_code'),
            'special_handling_codes': awb_data.get('special_handling_codes', []),
            'goods_descriptions': awb_data.get('goods_descriptions', []),
            'in_transit': is_in_transit,
            'xml_data': full_extracted_data
        }

    def log_processing_activity(self, result: Dict[str, Any]):
        """
        Log processing activity to the processing_log table.

        Args:
            result (dict): Processing result
        """
        try:
            self.db_ops.log_processing(
                file_name=result['file_name'],
                message_id=result['message_id'],
                msg_type='XFWB',
                awb_count=result['awb_count'],
                partial_count=0,  # XFWB doesn't create partials in Phase 1
                processing_time_ms=result['processing_time_ms'],
                success=result['success'],
                error_message='; '.join(result['errors']) if result['errors'] else None
            )
        except Exception as e:
            self.logger.error(f"Failed to log processing activity: {str(e)}")
