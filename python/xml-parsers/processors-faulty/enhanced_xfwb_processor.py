#!/usr/bin/env python3
"""
Enhanced XFWB (Master Waybill) Processor.

This processor implements the new business logic requirements:
- XFWB files processed to enrich AWB shells with official declarations
- Declared pieces, weight, and volume tracking from XFWB
- Party information (shipper, consignee) handling
- Commodity details and special handling codes
- Duplicate prevention using SHA-256 hashing
- Processing status lifecycle management
"""

import os
import time
from datetime import datetime
from typing import Dict, List, Optional, Any

from extractors.xfwb_extractor import XFWBExtractor
from database.enhanced_operations import EnhancedDatabaseOperations


class EnhancedXFWBProcessor:
    """
    Enhanced XFWB processor implementing the new cargo manifest processing logic.
    
    Key Features:
    - File-level duplicate prevention
    - AWB enrichment with official XFWB declarations
    - Party information handling (shipper, consignee)
    - Commodity details and special handling codes
    - Processing status lifecycle management
    """

    def __init__(self, db_connection, db_cursor, branch_id=1, user_id=1, logger=None):
        """
        Initialize enhanced XFWB processor.

        Args:
            db_connection: Database connection object
            db_cursor: Database cursor object
            branch_id (int): Current branch ID for in-transit logic
            user_id (int): Current user ID for audit trails
            logger: Logger instance
        """
        self.db_connection = db_connection
        self.db_cursor = db_cursor
        self.branch_id = branch_id
        self.user_id = user_id
        self.logger = logger

        # Initialize database operations
        self.db_ops = EnhancedDatabaseOperations(
            db_connection, db_cursor, branch_id, user_id, logger
        )

        # Initialize extractor
        self.extractor = XFWBExtractor(logger=logger)

    def process_xfwb_file(self, file_path: str) -> Dict[str, Any]:
        """
        Process XFWB file with enhanced business logic.

        Args:
            file_path (str): Path to XFWB XML file

        Returns:
            dict: Processing result with statistics and status
        """
        start_time = time.time()
        file_name = os.path.basename(file_path)
        
        # Start processing log
        log_id = self.db_ops.start_processing_log(file_name, 'XFWB')

        try:
            # Check for file-level duplicates
            file_hash = self.db_ops.generate_file_hash(file_path)
            existing_file = self.db_ops.check_file_duplicate(file_hash)
            
            if existing_file:
                self.db_ops.record_duplicate_attempt(file_hash)
                self.db_ops.mark_processing_duplicate(log_id)
                
                self.logger.info(f"Duplicate XFWB file detected: {file_name} "
                               f"(first processed: {existing_file['first_processed_at']})")
                
                return {
                    'success': False,
                    'file_name': file_name,
                    'message': 'Duplicate file detected',
                    'duplicate_info': existing_file,
                    'log_id': log_id
                }

            # Record file as being processed
            file_size = os.path.getsize(file_path)
            self.db_ops.record_file_processed(file_path, file_hash, 'XFWB', file_size)

            # Extract data from XML
            extraction_result = self.extractor.extract_from_file(file_path)
            
            if not extraction_result['success']:
                error_msg = f"XML extraction failed: {extraction_result.get('error', 'Unknown error')}"
                self.db_ops.complete_processing_log(log_id, False, error_message=error_msg)
                
                return {
                    'success': False,
                    'file_name': file_name,
                    'error': error_msg,
                    'log_id': log_id
                }

            # Process the extracted data
            processing_result = self._process_master_waybill(
                extraction_result['data'], 
                file_name, 
                log_id
            )

            # Calculate processing time
            processing_time_ms = int((time.time() - start_time) * 1000)
            processing_result['processing_time_ms'] = processing_time_ms

            # Complete processing log
            self.db_ops.complete_processing_log(
                log_id,
                processing_result['success'],
                processing_result.get('awb_count', 0),
                0,  # No ULDs in XFWB
                processing_result.get('error'),
                {
                    'awb_number': processing_result.get('awb_number'),
                    'awbs_processed': processing_result.get('awb_count', 0),
                    'enrichment_type': processing_result.get('enrichment_type')
                }
            )

            return processing_result

        except Exception as e:
            error_msg = f"XFWB processing failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            
            self.db_ops.complete_processing_log(log_id, False, error_message=error_msg)
            
            return {
                'success': False,
                'file_name': file_name,
                'error': error_msg,
                'log_id': log_id
            }

    def _process_master_waybill(self, data: Dict[str, Any], file_name: str, log_id: str) -> Dict[str, Any]:
        """
        Process master waybill data with enhanced business logic.

        Args:
            data (dict): Extracted XFWB data
            file_name (str): Source file name
            log_id (str): Processing log ID

        Returns:
            dict: Processing result
        """
        result = {
            'success': True,
            'file_name': file_name,
            'log_id': log_id,
            'awb_number': None,
            'awb_count': 0,
            'enrichment_type': None,
            'errors': []
        }

        try:
            # Get waybill data
            waybill_data = data.get('waybill', data)  # Handle different data structures
            awb_number = waybill_data.get('awb_number')
            
            if not awb_number:
                raise ValueError("AWB number not found in XFWB data")
            
            result['awb_number'] = awb_number

            # Enrich waybill data with additional processing info
            enriched_data = self._enrich_waybill_data(waybill_data, data)

            # Create or update master waybill
            master_awb, was_created = self.db_ops.create_or_update_master_waybill_enhanced(
                enriched_data, 'XFWB'
            )

            result['awb_count'] = 1
            result['enrichment_type'] = 'created' if was_created else 'updated'

            # Handle party information
            self._process_party_information(awb_number, waybill_data)

            # Handle commodity details
            self._process_commodity_details(awb_number, waybill_data)

            # Commit transaction
            self.db_connection.commit()

            self.logger.info(f"XFWB processing completed: AWB {awb_number} {result['enrichment_type']}")

        except Exception as e:
            # Rollback transaction on error
            self.db_connection.rollback()
            result['success'] = False
            result['error'] = str(e)
            self.logger.error(f"Master waybill processing failed: {str(e)}", exc_info=True)

        return result

    def _enrich_waybill_data(self, waybill_data: Dict[str, Any], full_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enrich waybill data with additional processing information.

        Args:
            waybill_data (dict): Basic waybill data
            full_data (dict): Full extracted data

        Returns:
            dict: Enriched waybill data
        """
        enriched = waybill_data.copy()
        
        # Add full XML data for storage
        enriched['xml_data'] = full_data
        
        # Process special handling codes
        if 'special_handling_codes' in waybill_data:
            shc_list = waybill_data['special_handling_codes']
            if isinstance(shc_list, list):
                enriched['special_handling_code'] = ','.join(shc_list)
            else:
                enriched['special_handling_code'] = str(shc_list)
        
        # Process goods descriptions
        if 'goods_descriptions' in waybill_data:
            goods_list = waybill_data['goods_descriptions']
            if isinstance(goods_list, list):
                enriched['description'] = '; '.join(goods_list)
            else:
                enriched['description'] = str(goods_list)
        
        # Set commodity flags based on special handling codes
        self._set_commodity_flags(enriched)
        
        return enriched

    def _set_commodity_flags(self, waybill_data: Dict[str, Any]) -> None:
        """
        Set commodity flags based on special handling codes.

        Args:
            waybill_data (dict): Waybill data to update
        """
        shc_codes = waybill_data.get('special_handling_codes', [])
        if isinstance(shc_codes, str):
            shc_codes = shc_codes.split(',')
        
        # Convert to uppercase for comparison
        shc_codes = [code.strip().upper() for code in shc_codes]
        
        # Set flags based on special handling codes
        waybill_data['is_mail'] = any(code in ['MAL', 'AO'] for code in shc_codes)
        waybill_data['is_human_remains'] = 'HUM' in shc_codes
        waybill_data['is_dangerous_goods'] = any(code in ['DGR', 'RRY', 'RRW', 'RRX'] for code in shc_codes)

    def _process_party_information(self, awb_number: str, waybill_data: Dict[str, Any]) -> None:
        """
        Process party information (shipper, consignee).

        Args:
            awb_number (str): AWB number
            waybill_data (dict): Waybill data containing party information
        """
        # Extract shipper information
        shipper_info = waybill_data.get('shipper', {})
        if shipper_info:
            # Update master waybill with shipper code
            if 'code' in shipper_info:
                self.db_ops.update_record(
                    'master_waybills',
                    {'shipper_code': shipper_info['code'], 'updated_at': datetime.now()},
                    'awb_number = %s',
                    (awb_number,)
                )

        # Extract consignee information
        consignee_info = waybill_data.get('consignee', {})
        if consignee_info:
            # Update master waybill with consignee code
            if 'code' in consignee_info:
                self.db_ops.update_record(
                    'master_waybills',
                    {'consignee_code': consignee_info['code'], 'updated_at': datetime.now()},
                    'awb_number = %s',
                    (awb_number,)
                )

    def _process_commodity_details(self, awb_number: str, waybill_data: Dict[str, Any]) -> None:
        """
        Process commodity details and update flags.

        Args:
            awb_number (str): AWB number
            waybill_data (dict): Waybill data containing commodity information
        """
        update_data = {'updated_at': datetime.now()}
        
        # Set commodity flags
        if waybill_data.get('is_mail'):
            update_data['is_mail'] = True
        if waybill_data.get('is_human_remains'):
            update_data['is_human_remains'] = True
        if waybill_data.get('is_dangerous_goods'):
            # Handle dangerous goods flag if column exists
            pass
        
        # Update currency and payment information
        if 'currency_code' in waybill_data:
            update_data['currency_code'] = waybill_data['currency_code']
        if 'prepaid_collect_indicator' in waybill_data:
            update_data['prepaid_collect_indicator'] = waybill_data['prepaid_collect_indicator']
        
        # Apply updates if any
        if len(update_data) > 1:  # More than just updated_at
            self.db_ops.update_record(
                'master_waybills',
                update_data,
                'awb_number = %s',
                (awb_number,)
            )
