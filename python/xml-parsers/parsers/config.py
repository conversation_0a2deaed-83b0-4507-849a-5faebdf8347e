#!/usr/bin/env python3
"""
Configuration module for XML parsers.

This module contains database configuration and other settings
used by the XML parsers.
"""

import os


# Database configuration - read from Laravel .env file
def load_laravel_env():
    """Load database configuration from Laravel .env file."""
    env_path = "/var/www/aircargomis/python/xml-parsers/laraval-side/.env"
    config = {
        "host": "localhost",
        "port": "5432",
        "database": "cargo_mis",
        "user": "postgres",
        "password": "postgres",
    }

    try:
        with open(env_path, "r") as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#") and "=" in line:
                    key, value = line.split("=", 1)
                    key = key.strip()
                    value = value.strip().strip('"').strip("'")

                    if key == "DB_HOST":
                        config["host"] = value
                    elif key == "DB_PORT":
                        config["port"] = value
                    elif key == "DB_DATABASE":
                        config["database"] = value
                    elif key == "DB_USERNAME":
                        config["user"] = value
                    elif key == "DB_PASSWORD":
                        config["password"] = value
    except FileNotFoundError:
        print(f"Warning: Laravel .env file not found at {env_path}, using defaults")
    except Exception as e:
        print(f"Warning: Error reading Laravel .env file: {e}, using defaults")

    return config


DATABASE_CONFIG = load_laravel_env()

# Logging configuration
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Parser configuration
DEFAULT_USER_ID = 1
DEFAULT_BRANCH_ID = 1
ENABLE_PROFILING = os.getenv("ENABLE_PROFILING", "false").lower() == "true"

# XML validation settings
VALIDATE_XML_SCHEMA = os.getenv("VALIDATE_XML_SCHEMA", "false").lower() == "true"
XML_SCHEMA_PATH = os.getenv("XML_SCHEMA_PATH", "schemas/")

# Performance settings
MAX_PROCESSING_TIME = int(os.getenv("MAX_PROCESSING_TIME", "300"))  # 5 minutes
BATCH_SIZE = int(os.getenv("BATCH_SIZE", "100"))
