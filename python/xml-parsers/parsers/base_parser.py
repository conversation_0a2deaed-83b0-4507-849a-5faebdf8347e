#!/usr/bin/env python3
"""
Base parser for IATA XML files.
"""

import os
import logging
from lxml import etree
from datetime import datetime
import psycopg2
import sys

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.namespaces import NAMESPACES
from config.database import DB_CONFIG
from utils.message_type_detector import MessageTypeDetector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("xml_parser.log"),
        logging.StreamHandler()
    ]
)

class BaseParser:
    """Base class for all XML parsers."""

    def __init__(self, xml_file=None, xml_string=None, xml_type=None):
        """
        Initialize the parser.

        Args:
            xml_file (str, optional): Path to the XML file.
            xml_string (str, optional): XML content as a string.
            xml_type (str, optional): Type of XML (xffm, xfwb, xfzb).
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.xml_file = xml_file
        self.xml_string = xml_string
        self.xml_type = xml_type
        self.root = None
        self.namespaces = NAMESPACES.get(xml_type, NAMESPACES.get('xffm'))
        self.db_connection = None
        self.db_cursor = None
        self.detector = MessageTypeDetector()

        # Connect to the database
        self._connect_to_db()

        # Parse the XML
        if xml_file:
            self._parse_from_file()
        elif xml_string:
            self._parse_from_string()

        # Auto-detect message type if not provided
        if not self.xml_type and self.root is not None:
            detected_type = self.detector.detect_from_element(self.root)
            if detected_type:
                self.xml_type = detected_type
                self.namespaces = NAMESPACES.get(detected_type, NAMESPACES.get('xffm'))
                self.logger.info(f"Auto-detected XML message type: {detected_type}")

    def _connect_to_db(self):
        """Connect to the PostgreSQL database."""
        try:
            self.db_connection = psycopg2.connect(
                host=DB_CONFIG['host'],
                database=DB_CONFIG['database'],
                user=DB_CONFIG['user'],
                password=DB_CONFIG['password'],
                port=DB_CONFIG['port']
            )
            self.db_cursor = self.db_connection.cursor()
            self.logger.info("Connected to the database")
        except Exception as e:
            self.logger.error(f"Error connecting to the database: {e}")
            raise

    def _parse_from_file(self):
        """Parse XML from a file."""
        try:
            if not os.path.exists(self.xml_file):
                self.logger.error(f"XML file not found: {self.xml_file}")
                raise FileNotFoundError(f"XML file not found: {self.xml_file}")

            # Use lxml for better XPath support
            parser = etree.XMLParser(remove_blank_text=True)
            tree = etree.parse(self.xml_file, parser)
            self.root = tree.getroot()
            self.logger.info(f"Successfully parsed XML file: {self.xml_file}")
        except Exception as e:
            self.logger.error(f"Error parsing XML file: {e}")
            raise

    def _parse_from_string(self):
        """Parse XML from a string."""
        try:
            # Use lxml for better XPath support
            parser = etree.XMLParser(remove_blank_text=True)
            self.root = etree.fromstring(self.xml_string.encode('utf-8'), parser)
            self.logger.info("Successfully parsed XML string")
        except Exception as e:
            self.logger.error(f"Error parsing XML string: {e}")
            raise

    def find_element(self, xpath, root=None):
        """
        Find an element using XPath.

        Args:
            xpath (str): XPath expression.
            root (Element, optional): Root element to search from.

        Returns:
            Element: The found element or None.
        """
        if root is None:
            root = self.root

        try:
            # Handle namespace prefixes in XPath
            if ':' in xpath:
                # Create a namespace-agnostic XPath by using local-name()
                parts = xpath.split('/')
                namespace_agnostic_parts = []

                for part in parts:
                    if part == '.' or part == '':
                        namespace_agnostic_parts.append(part)
                        continue

                    # Handle predicates
                    if '[' in part:
                        element_name, predicate = part.split('[', 1)
                        predicate = '[' + predicate  # Add the opening bracket back
                    else:
                        element_name = part
                        predicate = ''

                    # Remove namespace prefix if present
                    if ':' in element_name:
                        prefix, local_name = element_name.split(':', 1)
                        namespace_agnostic_parts.append(f"*[local-name()='{local_name}']{predicate}")
                    else:
                        namespace_agnostic_parts.append(f"{element_name}{predicate}")

                namespace_agnostic_xpath = '/'.join(namespace_agnostic_parts)
                result = root.xpath(namespace_agnostic_xpath)
                if result:
                    return result[0]

            # Fall back to regular find with namespaces
            return root.find(xpath, self.namespaces)
        except Exception as e:
            self.logger.error(f"Error finding element with XPath '{xpath}': {e}")
            return None

    def find_elements(self, xpath, root=None):
        """
        Find all elements matching an XPath.

        Args:
            xpath (str): XPath expression.
            root (Element, optional): Root element to search from.

        Returns:
            list: List of found elements.
        """
        if root is None:
            root = self.root

        try:
            # Handle namespace prefixes in XPath
            if ':' in xpath:
                # Create a namespace-agnostic XPath by using local-name()
                parts = xpath.split('/')
                namespace_agnostic_parts = []

                for part in parts:
                    if part == '.' or part == '':
                        namespace_agnostic_parts.append(part)
                        continue

                    # Handle predicates
                    if '[' in part:
                        element_name, predicate = part.split('[', 1)
                        predicate = '[' + predicate  # Add the opening bracket back
                    else:
                        element_name = part
                        predicate = ''

                    # Remove namespace prefix if present
                    if ':' in element_name:
                        prefix, local_name = element_name.split(':', 1)
                        namespace_agnostic_parts.append(f"*[local-name()='{local_name}']{predicate}")
                    else:
                        namespace_agnostic_parts.append(f"{element_name}{predicate}")

                namespace_agnostic_xpath = '/'.join(namespace_agnostic_parts)
                return root.xpath(namespace_agnostic_xpath)

            # Fall back to regular findall with namespaces
            return root.findall(xpath, self.namespaces)
        except Exception as e:
            self.logger.error(f"Error finding elements with XPath '{xpath}': {e}")
            return []

    def get_element_text(self, xpath, root=None, default=None):
        """
        Get the text of an element.

        Args:
            xpath (str): XPath expression.
            root (Element, optional): Root element to search from.
            default: Default value if element not found.

        Returns:
            str: Element text or default value.
        """
        element = self.find_element(xpath, root)
        if element is not None and element.text:
            return element.text.strip()
        return default

    def get_element_attribute(self, xpath, attribute, root=None, default=None):
        """
        Get an attribute of an element.

        Args:
            xpath (str): XPath expression.
            attribute (str): Attribute name.
            root (Element, optional): Root element to search from.
            default: Default value if attribute not found.

        Returns:
            str: Attribute value or default value.
        """
        element = self.find_element(xpath, root)
        if element is not None and attribute in element.attrib:
            return element.attrib[attribute]
        return default

    def save_to_db(self, table, data, unique_fields=None):
        """
        Save data to the database.

        Args:
            table (str): Table name.
            data (dict): Data to save.
            unique_fields (list, optional): Fields to use for uniqueness check.

        Returns:
            int: ID of the inserted/updated record.
        """
        try:
            # Convert any datetime objects to strings
            for key, value in data.items():
                if isinstance(value, datetime):
                    data[key] = value.isoformat()

            # If unique_fields is provided, try to update existing record
            if unique_fields:
                where_clause = " AND ".join([f"{field} = %s" for field in unique_fields])
                where_values = [data[field] for field in unique_fields]

                # Check if record exists
                self.db_cursor.execute(
                    f"SELECT id FROM {table} WHERE {where_clause}",
                    where_values
                )
                result = self.db_cursor.fetchone()

                if result:
                    # Update existing record
                    record_id = result[0]
                    set_clause = ", ".join([f"{key} = %s" for key in data.keys() if key != 'id'])
                    set_values = [value for key, value in data.items() if key != 'id']

                    self.db_cursor.execute(
                        f"UPDATE {table} SET {set_clause} WHERE id = %s",
                        set_values + [record_id]
                    )
                    self.db_connection.commit()
                    self.logger.info(f"Updated record in {table} with ID {record_id}")
                    return record_id

            # Insert new record
            columns = ", ".join(data.keys())
            placeholders = ", ".join(["%s"] * len(data))
            values = list(data.values())

            self.db_cursor.execute(
                f"INSERT INTO {table} ({columns}) VALUES ({placeholders}) RETURNING id",
                values
            )
            record_id = self.db_cursor.fetchone()[0]
            self.db_connection.commit()
            self.logger.info(f"Inserted new record in {table} with ID {record_id}")
            return record_id

        except Exception as e:
            self.db_connection.rollback()
            self.logger.error(f"Error saving to database: {e}")
            raise

    def detect_message_type(self) -> str:
        """
        Detect the message type of the currently loaded XML.

        Returns:
            str: Detected message type ('xffm', 'xfwb', 'xfzb') or None
        """
        if self.root is not None:
            return self.detector.detect_from_element(self.root)
        return None

    @staticmethod
    def detect_message_type_from_file(xml_file: str) -> str:
        """
        Detect message type from an XML file without fully parsing it.

        Args:
            xml_file (str): Path to the XML file

        Returns:
            str: Detected message type ('xffm', 'xfwb', 'xfzb') or None
        """
        detector = MessageTypeDetector()
        return detector.detect_from_file(xml_file)

    @staticmethod
    def detect_message_type_from_string(xml_string: str) -> str:
        """
        Detect message type from an XML string without fully parsing it.

        Args:
            xml_string (str): XML content as string

        Returns:
            str: Detected message type ('xffm', 'xfwb', 'xfzb') or None
        """
        detector = MessageTypeDetector()
        return detector.detect_from_string(xml_string)

    def close(self):
        """Close the database connection."""
        if self.db_connection:
            self.db_cursor.close()
            self.db_connection.close()
            self.logger.info("Database connection closed")

    def __del__(self):
        """Destructor to ensure database connection is closed."""
        self.close()
