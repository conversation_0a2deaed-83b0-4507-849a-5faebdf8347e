#!/usr/bin/env python3
"""
XFWB Parser using modular architecture.

This module provides the main XFWB parser class that inherits from BaseXMLParser
and uses modular extractors, validators, and database operations.
"""

import os
import sys

from lxml import etree

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.xfwb_operations import XFWBDatabaseOperations
from extractors.xfwb_extractor import XFWBExtractor
from parsers.base_xml_parser import BaseXMLParser
from validators.xfwb_validator import XFWBValidator
from utils.awb_utils import normalize_awb_number


class XFWBParser(BaseXMLParser):
    """
    XFWB (Master Air Waybill) parser using modular architecture.

    This parser inherits from BaseXMLParser and uses separate modules for:
    - Data extraction (XFWBExtractor)
    - Data validation (XFWBValidator)
    - Database operations (XFWBDatabaseOperations)
    """

    def __init__(self, user_id=1, branch_id=1, enable_profiling=False):
        """
        Initialize the XFWB parser.

        Args:
            user_id (int): User ID for database operations.
            branch_id (int): Branch ID for database operations.
            enable_profiling (bool): Whether to enable profiling.
        """
        super().__init__(user_id, branch_id, enable_profiling)

        # Initialize modular components
        self.extractor = XFWBExtractor(self.logger)
        self.validator = XFWBValidator(self.logger)
        self.db_operations = XFWBDatabaseOperations(
            self.db_connection, self.db_cursor, user_id, branch_id, self.logger
        )

        self.logger.info("XFWB Parser initialized with modular architecture")

    def parse_file(self, xml_file_path):
        """
        Parse an XFWB XML file.

        Args:
            xml_file_path (str): Path to the XML file.

        Returns:
            dict: Parsing result with extracted data and status.
        """
        self.logger.info(f"Starting to parse XFWB file: {xml_file_path}")

        if self.enable_profiling:
            self.start_profiling()

        result = {
            "success": False,
            "data": None,
            "errors": [],
            "warnings": [],
            "awb_id": None,
        }

        try:
            # Read and parse XML file
            with open(xml_file_path, "r", encoding="utf-8") as file:
                xml_content = file.read()

            return self.parse_string(xml_content)

        except FileNotFoundError:
            error_msg = f"XML file not found: {xml_file_path}"
            result["errors"].append(error_msg)
            self.logger.error(error_msg)
        except Exception as e:
            error_msg = f"Error reading XML file: {e}"
            result["errors"].append(error_msg)
            self.logger.error(error_msg)
        finally:
            if self.enable_profiling:
                self.stop_profiling()

        return result

    def parse_string(self, xml_string):
        """
        Parse an XFWB XML string.

        Args:
            xml_string (str): XML content as string.

        Returns:
            dict: Parsing result with extracted data and status.
        """
        self.logger.info("Starting to parse XFWB XML string")

        if self.enable_profiling:
            self.start_profiling()

        result = {
            "success": False,
            "data": None,
            "errors": [],
            "warnings": [],
            "awb_id": None,
        }

        try:
            # Parse XML
            root = self.parse_xml_string(xml_string)
            if root is None:
                result["errors"].append("Failed to parse XML")
                return result

            # Extract data
            self.logger.info("Extracting data from XML")
            extracted_data = self.extractor.extract(root)

            # Check if AWB number was found
            if "awb_number" not in extracted_data or not extracted_data["awb_number"]:
                # Try to find AWB number in the XML content as a last resort
                try:
                    from xml.etree.ElementTree import tostring
                    from utils.awb_utils import extract_awb_from_text
                    
                    # Convert XML to string and try to extract AWB
                    xml_str = tostring(root, encoding='utf-8').decode('utf-8')
                    potential_awb = extract_awb_from_text(xml_str)
                    
                    if potential_awb:
                        self.logger.info(f"Found AWB number {potential_awb} using text extraction fallback")
                        extracted_data["awb_number"] = potential_awb
                    else:
                        error_msg = "AWB number not found in XFWB file"
                        result["errors"].append(error_msg)
                        self.logger.error(error_msg)
                        return result
                except Exception as e:
                    error_msg = f"AWB number not found in XFWB file and fallback extraction failed: {e}"
                    result["errors"].append("AWB number not found in XFWB file")
                    self.logger.error(error_msg)
                    return result

            # Normalize AWB number to IATA format
            extracted_data["awb_number"] = normalize_awb_number(
                extracted_data["awb_number"]
            )
            
            # Validate AWB number format
            from utils.awb_utils import is_valid_awb_format
            if not is_valid_awb_format(extracted_data["awb_number"]):
                self.logger.warning(f"AWB number {extracted_data['awb_number']} has non-standard format")
                # We continue processing as normalize_awb_number should have handled it

            if not extracted_data:
                result["errors"].append("No data extracted from XML")
                return result

            # Add original XML content to extracted data for storage
            extracted_data["xml_content"] = xml_string

            self.logger.info(
                f"Extracted data for AWB: {extracted_data.get('awb_number', 'Unknown')}"
            )

            # Validate data
            self.logger.info("Validating extracted data")
            is_valid = self.validator.validate(extracted_data)

            # Collect validation errors and warnings
            result["errors"].extend(
                [error["message"] for error in self.validator.get_errors()]
            )
            result["warnings"].extend(
                [warning["message"] for warning in self.validator.get_warnings()]
            )

            if not is_valid:
                self.logger.error("Data validation failed")
                result["data"] = (
                    extracted_data  # Include data even if validation failed
                )
                return result

            # Save to database
            self.logger.info("Saving data to database")
            save_result = self.db_operations.save_data(extracted_data)

            if save_result["success"]:
                result["success"] = True
                result["data"] = extracted_data
                result["awb_id"] = save_result["awb_id"]
                result["errors"].extend(save_result.get("errors", []))
                result["warnings"].extend(save_result.get("warnings", []))

                self.logger.info(
                    f"Successfully processed XFWB for AWB {extracted_data['awb_number']}"
                )
            else:
                result["errors"].extend(save_result.get("errors", []))
                result["data"] = extracted_data
                self.logger.error("Failed to save data to database")

        except Exception as e:
            error_msg = f"Unexpected error during XFWB parsing: {e}"
            result["errors"].append(error_msg)
            self.logger.error(error_msg, exc_info=True)
        finally:
            if self.enable_profiling:
                self.stop_profiling()

        return result

    def parse_xml_string(self, xml_string):
        """
        Parse XML string into an Element tree.

        Args:
            xml_string (str): XML content as string.

        Returns:
            Element: Root element or None if parsing failed.
        """
        try:
            # Remove BOM if present
            if xml_string.startswith("\ufeff"):
                xml_string = xml_string[1:]

            # Parse XML
            parser = etree.XMLParser(strip_cdata=False, recover=True)
            root = etree.fromstring(xml_string.encode("utf-8"), parser)

            self.logger.info("XML parsed successfully")
            return root

        except etree.XMLSyntaxError as e:
            self.logger.error(f"XML syntax error: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error parsing XML: {e}")
            return None

    def get_awb_summary(self, awb_number):
        """
        Get summary information for an AWB.

        Args:
            awb_number (str): AWB number.

        Returns:
            dict: AWB summary or None if not found.
        """
        try:
            return self.db_operations.get_awb_summary(awb_number)
        except Exception as e:
            self.logger.error(f"Error getting AWB summary: {e}")
            return None

    def update_awb_status(self, awb_number, status, notes=None):
        """
        Update AWB status.

        Args:
            awb_number (str): AWB number.
            status (str): New status.
            notes (str): Optional status notes.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            rows_affected = self.db_operations.update_awb_status(
                awb_number, status, notes
            )
            if rows_affected > 0:
                self.logger.info(f"Updated status for AWB {awb_number} to {status}")
                return True
            else:
                self.logger.warning(f"No rows affected when updating AWB {awb_number}")
                return False
        except Exception as e:
            self.logger.error(f"Error updating AWB status: {e}")
            return False

    def validate_xml_only(self, xml_file_path):
        """
        Validate XML file without saving to database.

        Args:
            xml_file_path (str): Path to the XML file.

        Returns:
            dict: Validation result.
        """
        result = {"valid": False, "data": None, "errors": [], "warnings": []}

        try:
            # Read and parse XML file
            with open(xml_file_path, "r", encoding="utf-8") as file:
                xml_content = file.read()

            # Parse XML
            root = self.parse_xml_string(xml_content)
            if root is None:
                result["errors"].append("Failed to parse XML")
                return result

            # Extract data
            extracted_data = self.extractor.extract(root)
            if not extracted_data:
                result["errors"].append("No data extracted from XML")
                return result

            # Add original XML content to extracted data for storage
            extracted_data["xml_content"] = xml_content

            # Validate data
            is_valid = self.validator.validate(extracted_data)

            result["valid"] = is_valid
            result["data"] = extracted_data
            result["errors"] = [
                error["message"] for error in self.validator.get_errors()
            ]
            result["warnings"] = [
                warning["message"] for warning in self.validator.get_warnings()
            ]

        except Exception as e:
            result["errors"].append(f"Error during validation: {e}")

        return result
