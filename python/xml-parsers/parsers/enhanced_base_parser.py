#!/usr/bin/env python3
"""
Enhanced Base Parser with Comprehensive Validation and Quality Assessment.

This enhanced parser integrates all the improvement recommendations including
validation framework, data quality assessment, CIMP processing, ONE Record
conversion, and performance monitoring.
"""

import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime
import sys
import os

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from parsers.base_parser import BaseParser
from validation.validation_framework import ValidationResult, AWBValidator
from validation.data_quality import DataQualityAssessment, DataQualityMetrics
from processors.cimp_processor import CIMPSegmentProcessor
from converters.one_record_converter import OneRecordConverter
from monitoring.performance_monitor import PerformanceMonitor
from services.uld_service import ULDService
from config.parser_config import ParserConfiguration


class EnhancedBaseParser(BaseParser):
    """Enhanced base parser with comprehensive validation and quality assessment."""
    
    def __init__(self, xml_file: Optional[str] = None, xml_string: Optional[str] = None, 
                 xml_type: Optional[str] = None, config_file: Optional[str] = None,
                 user_id: int = 1, branch_id: int = 1):
        """
        Initialize enhanced parser.
        
        Args:
            xml_file: Path to XML file
            xml_string: XML content as string
            xml_type: Type of XML (xfwb, xfzb, xffm)
            config_file: Path to configuration file
            user_id: User ID for database operations
            branch_id: Branch ID for database operations
        """
        # Initialize base parser
        super().__init__(xml_file, xml_string, xml_type)
        
        # Initialize configuration
        self.config = ParserConfiguration(config_file)
        
        # Initialize validation framework
        self.validation_result = ValidationResult()
        self.awb_validator = AWBValidator(self.validation_result)
        self.quality_assessor = DataQualityAssessment()
        
        # Initialize processors and converters
        self.cimp_processor = CIMPSegmentProcessor(self, self.validation_result)
        self.one_record_converter = OneRecordConverter(
            self.config.get('conversion', 'ontology_version', '2.1')
        )
        
        # Initialize services
        self.uld_service = ULDService(self.db_connection, self.config.config) if self.db_connection else None
        
        # Initialize monitoring
        self.performance_monitor = PerformanceMonitor(self.config.config)
        
        # Parser settings
        self.user_id = user_id
        self.branch_id = branch_id
        self.strict_mode = self.config.is_strict_mode()
        self.fail_on_errors = self.config.should_fail_on_errors()
        
        self.logger.info(f"Enhanced parser initialized for {xml_type} with strict_mode={self.strict_mode}")
    
    def parse_with_validation(self) -> Dict[str, Any]:
        """
        Parse XML with comprehensive validation and quality assessment.
        
        Returns:
            Dictionary containing parsed data and assessment results
        """
        start_time = time.time()
        
        try:
            self.logger.info("Starting enhanced XML parsing with validation")
            
            # Pre-validation checks
            self._validate_xml_structure()
            
            # Extract data using CIMP segments if enabled
            if self.config.get('cimp_processing', 'enable_segment_processing', True):
                waybill_data = self.cimp_processor.process_all_segments(self.root)
            else:
                waybill_data = self._extract_data_legacy()
            
            # Post-extraction validation
            self._validate_extracted_data(waybill_data)
            
            # Quality assessment
            quality_metrics = self.quality_assessor.assess_waybill_quality(waybill_data)
            
            # Process ULD information if enabled
            uld_data = None
            if self.config.is_uld_tracking_enabled() and self.uld_service:
                uld_data = self._process_uld_data(waybill_data)
            
            # Convert to multiple formats if needed
            outputs = self._create_outputs(waybill_data, quality_metrics, uld_data)
            
            # Finalize validation
            self.validation_result.finalize()
            
            # Check if parsing should fail
            if self.fail_on_errors and self.validation_result.has_errors():
                raise ValueError(f"Parsing failed due to validation errors: {len(self.validation_result.errors)} errors found")
            
            # Record performance metrics
            processing_time_ms = int((time.time() - start_time) * 1000)
            file_name = self.xml_file or "xml_string"
            self.performance_monitor.record_processing_metrics(
                file_name, processing_time_ms, self.validation_result, quality_metrics
            )
            
            self.logger.info(f"Enhanced parsing completed in {processing_time_ms}ms")
            return outputs
            
        except Exception as e:
            self.validation_result.add_error('PARSING_FAILED', str(e))
            self.validation_result.finalize()
            
            # Record failed processing
            processing_time_ms = int((time.time() - start_time) * 1000)
            file_name = self.xml_file or "xml_string"
            
            # Create dummy quality metrics for failed parsing
            failed_quality = DataQualityMetrics()
            failed_quality.overall_score = 0.0
            
            self.performance_monitor.record_processing_metrics(
                file_name, processing_time_ms, self.validation_result, failed_quality
            )
            
            self.logger.error(f"Enhanced parsing failed: {e}")
            return self._create_error_response(e)
    
    def _validate_xml_structure(self) -> None:
        """Validate basic XML structure."""
        if self.root is None:
            self.validation_result.add_error('INVALID_XML', 'XML root element is None')
            return
        
        # Check for required namespaces
        if not self.root.nsmap:
            self.validation_result.add_warning('MISSING_NAMESPACES', 'XML document has no namespace declarations')
        
        # Check for TypeCode element
        type_code = self.get_element_text('.//TypeCode', self.root)
        if not type_code:
            self.validation_result.add_error('MISSING_TYPE_CODE', 'TypeCode element is required')
    
    def _extract_data_legacy(self) -> Dict[str, Any]:
        """Extract data using legacy method (fallback)."""
        # This would call the original extraction methods
        # For now, return empty dict as placeholder
        self.logger.warning("Using legacy data extraction method")
        return {}
    
    def _validate_extracted_data(self, waybill_data: Dict[str, Any]) -> None:
        """Validate extracted waybill data."""
        # Use AWB validator
        self.awb_validator.validate(waybill_data)
        
        # Additional custom validations
        self._validate_business_rules(waybill_data)
    
    def _validate_business_rules(self, waybill_data: Dict[str, Any]) -> None:
        """Validate business-specific rules."""
        # Check for required fields based on configuration
        required_fields = self.config.get('validation', 'required_fields', [])
        for field in required_fields:
            if not waybill_data.get(field):
                self.validation_result.add_error(
                    'MISSING_REQUIRED_FIELD',
                    f'Required field missing: {field}',
                    field
                )
        
        # Validate weight and pieces relationship
        weight = waybill_data.get('total_weight')
        pieces = waybill_data.get('total_pieces')
        if weight and pieces:
            try:
                weight_per_piece = float(weight) / int(pieces)
                if weight_per_piece < 0.01:
                    self.validation_result.add_warning(
                        'SUSPICIOUS_WEIGHT_RATIO',
                        f'Very low weight per piece: {weight_per_piece:.3f} kg',
                        'total_weight'
                    )
                elif weight_per_piece > 1000:
                    self.validation_result.add_warning(
                        'SUSPICIOUS_WEIGHT_RATIO',
                        f'Very high weight per piece: {weight_per_piece:.1f} kg',
                        'total_weight'
                    )
            except (ValueError, ZeroDivisionError):
                self.validation_result.add_warning(
                    'INVALID_WEIGHT_PIECES',
                    'Cannot calculate weight per piece ratio'
                )
    
    def _process_uld_data(self, waybill_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process ULD data if ULD tracking is enabled."""
        try:
            if not self.uld_service:
                return None
            
            # Extract ULD information from waybill data
            manifest_data = {
                'manifest_id': waybill_data.get('manifest_id'),
                'uld_details': waybill_data.get('uld_details', []),
                'awb_allocations': waybill_data.get('awb_allocations', [])
            }
            
            # Process ULD data
            processed_ulds = self.uld_service.process_uld_from_manifest(
                manifest_data, self.branch_id, self.user_id
            )
            
            return {
                'processed_ulds': processed_ulds,
                'uld_count': len(processed_ulds)
            }
            
        except Exception as e:
            self.validation_result.add_warning(
                'ULD_PROCESSING_FAILED',
                f'ULD processing failed: {str(e)}'
            )
            self.logger.warning(f"ULD processing failed: {e}")
            return None
    
    def _create_outputs(self, waybill_data: Dict[str, Any], 
                       quality_metrics: DataQualityMetrics,
                       uld_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Create multiple output formats."""
        outputs = {
            'database_record': waybill_data,
            'validation_report': self.validation_result.to_dict(),
            'quality_metrics': quality_metrics.to_dict()
        }
        
        # Add ULD data if available
        if uld_data:
            outputs['uld_data'] = uld_data
        
        # Add ONE Record conversion if enabled
        output_formats = self.config.get('conversion', 'output_formats', ['database'])
        if 'one_record' in output_formats:
            try:
                one_record_data = self.one_record_converter.convert_waybill_to_one_record(waybill_data)
                outputs['one_record_json'] = one_record_data
                
                # Validate ONE Record format
                validation_issues = self.one_record_converter.validate_one_record_format(one_record_data)
                if validation_issues:
                    for issue in validation_issues:
                        self.validation_result.add_warning('ONE_RECORD_VALIDATION', issue)
                
            except Exception as e:
                self.validation_result.add_error(
                    'ONE_RECORD_CONVERSION_FAILED',
                    f'ONE Record conversion failed: {str(e)}'
                )
                self.logger.error(f"ONE Record conversion failed: {e}")
        
        return outputs
    
    def _create_error_response(self, error: Exception) -> Dict[str, Any]:
        """Create error response."""
        return {
            'success': False,
            'error': str(error),
            'validation_report': self.validation_result.to_dict(),
            'quality_metrics': {
                'overall_score': 0.0,
                'completeness_score': 0.0,
                'accuracy_score': 0.0,
                'consistency_score': 0.0
            }
        }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics."""
        return self.performance_monitor.get_current_metrics()
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get parser health status."""
        return self.performance_monitor.get_health_status()
    
    def export_metrics(self, format: str = 'json') -> str:
        """Export performance metrics."""
        return self.performance_monitor.export_metrics(format)
    
    def reset_metrics(self) -> None:
        """Reset performance metrics."""
        self.performance_monitor.reset_metrics()
    
    def cleanup(self) -> None:
        """Cleanup resources."""
        if self.performance_monitor:
            self.performance_monitor.stop_monitoring()
        
        # Close database connection
        if self.db_connection:
            try:
                self.db_connection.close()
            except Exception as e:
                self.logger.warning(f"Error closing database connection: {e}")
        
        self.logger.info("Enhanced parser cleanup completed")
