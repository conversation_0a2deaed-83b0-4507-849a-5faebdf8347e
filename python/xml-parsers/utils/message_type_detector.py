#!/usr/bin/env python3
"""
XML Message Type Detector for IATA XML files.
Automatically detects message type (XFFM, XFWB, XFZB) based on TypeCode elements.
"""

import logging
from lxml import etree
from typing import Optional, Union


class MessageTypeDetector:
    """
    Detects XML message type based on TypeCode element values.

    Detection Rules:
    - XFFM (Flight Manifest): TypeCode = 122 with specific attributes
    - XFWB (Air Waybill): TypeCode = 741 (Master with houses) or 740 (Direct without houses)
    - XFZB (House Air Waybill): TypeCode = 703
    """

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

    def detect_from_file(self, xml_file: str) -> Optional[str]:
        """
        Detect message type from XML file.

        Args:
            xml_file (str): Path to the XML file

        Returns:
            str: Message type ('xffm', 'xfwb', 'xfzb') or None if not detected
        """
        try:
            parser = etree.XMLParser(remove_blank_text=True)
            tree = etree.parse(xml_file, parser)
            root = tree.getroot()
            return self._detect_from_element(root)
        except Exception as e:
            self.logger.error(f"Error parsing XML file {xml_file}: {e}")
            return None

    def detect_from_string(self, xml_string: str) -> Optional[str]:
        """
        Detect message type from XML string.

        Args:
            xml_string (str): XML content as string

        Returns:
            str: Message type ('xffm', 'xfwb', 'xfzb') or None if not detected
        """
        try:
            parser = etree.XMLParser(remove_blank_text=True)
            root = etree.fromstring(xml_string.encode('utf-8'), parser)
            return self._detect_from_element(root)
        except Exception as e:
            self.logger.error(f"Error parsing XML string: {e}")
            return None

    def detect_from_element(self, root_element) -> Optional[str]:
        """
        Detect message type from XML element.

        Args:
            root_element: XML root element

        Returns:
            str: Message type ('xffm', 'xfwb', 'xfzb') or None if not detected
        """
        return self._detect_from_element(root_element)

    def _detect_from_element(self, root) -> Optional[str]:
        """
        Internal method to detect message type from XML element.

        Args:
            root: XML root element

        Returns:
            str: Message type ('xffm', 'xfwb', 'xfzb') or None if not detected
        """
        try:
            # Find all TypeCode elements regardless of namespace prefix
            type_code_elements = root.xpath('.//*[local-name()="TypeCode"]')

            if not type_code_elements:
                self.logger.warning("No TypeCode elements found in XML")
                return None

            # Check each TypeCode element
            for type_code_elem in type_code_elements:
                if type_code_elem.text is None:
                    continue

                type_code_value = type_code_elem.text.strip()

                # Check for XFFM (Flight Manifest)
                if type_code_value == "122":
                    # Verify XFFM attributes
                    if self._is_xffm_type_code(type_code_elem):
                        self.logger.info(f"Detected XFFM message type (TypeCode: {type_code_value})")
                        return "xffm"

                # Check for XFWB (Air Waybill - Master or Direct)
                elif type_code_value in ["741", "740"]:
                    if type_code_value == "741":
                        self.logger.info(f"Detected XFWB message type (TypeCode: {type_code_value} - Master AWB with houses)")
                    else:
                        self.logger.info(f"Detected XFWB message type (TypeCode: {type_code_value} - Direct AWB without houses)")
                    return "xfwb"

                # Check for XFZB (House Air Waybill)
                elif type_code_value == "703":
                    self.logger.info(f"Detected XFZB message type (TypeCode: {type_code_value})")
                    return "xfzb"

            # Log all found TypeCode values for debugging
            type_code_values = [elem.text.strip() for elem in type_code_elements if elem.text]
            self.logger.warning(f"No matching TypeCode found. Found values: {type_code_values}")
            return None

        except Exception as e:
            self.logger.error(f"Error detecting message type: {e}")
            return None

    def _is_xffm_type_code(self, type_code_elem) -> bool:
        """
        Verify if TypeCode element matches XFFM pattern.

        For XFFM, TypeCode should be 122 with specific attributes:
        - listID="1001"
        - listAgencyID="6"
        - listVersionID="D09A"

        Args:
            type_code_elem: TypeCode XML element

        Returns:
            bool: True if matches XFFM pattern
        """
        try:
            # Check required attributes for XFFM
            list_id = type_code_elem.get('listID')
            list_agency_id = type_code_elem.get('listAgencyID')
            list_version_id = type_code_elem.get('listVersionID')

            # XFFM should have these specific attribute values
            if (list_id == "1001" and
                list_agency_id == "6" and
                list_version_id == "D09A"):
                return True

            # If attributes don't match exactly, log for debugging
            if any([list_id, list_agency_id, list_version_id]):
                self.logger.debug(f"TypeCode 122 found but attributes don't match XFFM pattern: "
                                f"listID={list_id}, listAgencyID={list_agency_id}, "
                                f"listVersionID={list_version_id}")

            return False

        except Exception as e:
            self.logger.error(f"Error checking XFFM TypeCode attributes: {e}")
            return False

    def get_supported_types(self) -> list:
        """
        Get list of supported message types.

        Returns:
            list: List of supported message types
        """
        return ["xffm", "xfwb", "xfzb"]

    def get_detection_rules(self) -> dict:
        """
        Get detection rules for each message type.

        Returns:
            dict: Detection rules for each message type
        """
        return {
            "xffm": {
                "type_code": "122",
                "required_attributes": {
                    "listID": "1001",
                    "listAgencyID": "6",
                    "listVersionID": "D09A"
                },
                "description": "Flight Manifest"
            },
            "xfwb": {
                "type_code": ["741", "740"],
                "required_attributes": {},
                "description": "Air Waybill (741=Master with houses, 740=Direct without houses)"
            },
            "xfzb": {
                "type_code": "703",
                "required_attributes": {},
                "description": "House Air Waybill"
            }
        }
