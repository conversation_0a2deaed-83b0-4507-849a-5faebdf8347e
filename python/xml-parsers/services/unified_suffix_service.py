#!/usr/bin/env python3
"""
Unified Partial Suffix Generation Service.

This service provides consistent suffix generation logic that matches
the Laravel PartialSuffixTracking model to prevent conflicts between
Python and Laravel implementations.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime


class UnifiedSuffixService:
    """
    Unified suffix generation service that matches Laravel implementation.

    This ensures consistent suffix generation across Python and Laravel layers
    to prevent conflicts and maintain data integrity.
    """

    def __init__(self, db_connection, db_cursor, branch_id: int, logger=None):
        """
        Initialize unified suffix service.

        Args:
            db_connection: Database connection object
            db_cursor: Database cursor object
            branch_id (int): Current branch ID
            logger: Logger instance
        """
        self.db_connection = db_connection
        self.db_cursor = db_cursor
        self.branch_id = branch_id
        self.logger = logger or logging.getLogger(__name__)

    def generate_suffix(self, awb_number: str, manifest_id: str, flight_number: str,
                       flight_date: str, split_type: str) -> str:
        """
        Generate suffix for partial shipments (P and D types).

        This method exactly matches the Laravel PartialSuffixTracking::generateSuffix
        method to ensure consistency across implementations.

        Args:
            awb_number (str): AWB number
            manifest_id (str): Manifest ID
            flight_number (str): Flight number
            flight_date (str): Flight date
            split_type (str): P or D

        Returns:
            str: Generated suffix (e.g., "AWB-1", "AWB-2")
        """
        try:
            # Step 1: Check if suffix already exists for this flight (matches Laravel logic)
            existing_suffix = self._get_existing_suffix(awb_number, manifest_id)
            if existing_suffix:
                self.logger.info(f"Using existing suffix for AWB {awb_number} on manifest {manifest_id}: {existing_suffix}")
                return existing_suffix

            # Step 2: Get the next suffix number for this AWB (matches Laravel logic)
            next_suffix_number = self._get_next_suffix_number(awb_number)
            generated_suffix = f"{awb_number}-{next_suffix_number}"

            # Step 3: Create the tracking record (matches Laravel structure)
            self._create_suffix_tracking_record(
                awb_number, manifest_id, flight_number, flight_date,
                next_suffix_number, generated_suffix, split_type
            )

            self.logger.info(f"Generated new suffix for AWB {awb_number}: {generated_suffix}")
            return generated_suffix

        except Exception as e:
            self.logger.error(f"Error generating suffix for AWB {awb_number}: {str(e)}")
            # Fallback to simple suffix generation
            return f"{awb_number}-1"

    def _get_existing_suffix(self, awb_number: str, manifest_id: str) -> Optional[str]:
        """
        Check if suffix already exists for this AWB on this manifest.

        Args:
            awb_number (str): AWB number
            manifest_id (str): Manifest ID

        Returns:
            str: Existing suffix if found, None otherwise
        """
        try:
            # Check if partial_suffix_tracking table exists
            if not hasattr(self, '_suffix_tracking_available'):
                try:
                    self.db_cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_name = 'partial_suffix_tracking'
                        )
                    """)
                    self._suffix_tracking_available = self.db_cursor.fetchone()[0]
                except:
                    self._suffix_tracking_available = False

            if not self._suffix_tracking_available:
                # Table doesn't exist, return None (will generate new suffix)
                return None

            # Matches Laravel: where('awb_number', $awbNumber)->where('manifest_id', $manifestId)->first()
            self.db_cursor.execute("""
                SELECT generated_suffix
                FROM partial_suffix_tracking
                WHERE awb_number = %s AND manifest_id = %s
                LIMIT 1
            """, (awb_number, manifest_id))

            result = self.db_cursor.fetchone()
            return result[0] if result else None

        except Exception as e:
            self.logger.error(f"Error checking existing suffix for AWB {awb_number}: {str(e)}")
            return None

    def _get_next_suffix_number(self, awb_number: str) -> int:
        """
        Get the next suffix number for this AWB.

        Args:
            awb_number (str): AWB number

        Returns:
            int: Next suffix number
        """
        try:
            # Check if table exists
            if not getattr(self, '_suffix_tracking_available', False):
                return 1

            # Matches Laravel: where('awb_number', $awbNumber)->max('suffix_number') ?? 0
            self.db_cursor.execute("""
                SELECT COALESCE(MAX(suffix_number), 0)
                FROM partial_suffix_tracking
                WHERE awb_number = %s
            """, (awb_number,))

            result = self.db_cursor.fetchone()
            max_suffix = result[0] if result else 0

            return max_suffix + 1

        except Exception as e:
            self.logger.error(f"Error getting next suffix number for AWB {awb_number}: {str(e)}")
            return 1

    def _create_suffix_tracking_record(self, awb_number: str, manifest_id: str,
                                     flight_number: str, flight_date: str,
                                     suffix_number: int, generated_suffix: str,
                                     split_type: str) -> None:
        """
        Create suffix tracking record in database.

        Args:
            awb_number (str): AWB number
            manifest_id (str): Manifest ID
            flight_number (str): Flight number
            flight_date (str): Flight date
            suffix_number (int): Suffix number
            generated_suffix (str): Generated suffix
            split_type (str): Split type (P or D)
        """
        try:
            # Check if table exists
            if not getattr(self, '_suffix_tracking_available', False):
                self.logger.info(f"Suffix tracking table not available, skipping record creation for: {generated_suffix}")
                return

            # Matches Laravel PartialSuffixTracking::create structure
            self.db_cursor.execute("""
                INSERT INTO partial_suffix_tracking (
                    awb_number, flight_number, flight_date, manifest_id,
                    suffix_number, generated_suffix, split_type, branch_id, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """, (
                awb_number, flight_number, flight_date, manifest_id,
                suffix_number, generated_suffix, split_type, self.branch_id,
                datetime.now()
            ))

            self.logger.info(f"Created suffix tracking record: {generated_suffix}")

        except Exception as e:
            self.logger.error(f"Error creating suffix tracking record: {str(e)}")
            # Don't raise if table doesn't exist, just log and continue
            if "does not exist" in str(e):
                self.logger.warning("Suffix tracking table not available, continuing without tracking")
            else:
                raise

    def requires_suffix(self, split_type: str) -> bool:
        """
        Check if a split type requires suffix generation.

        Args:
            split_type (str): Split type code

        Returns:
            bool: True if suffix is required
        """
        # Matches Laravel PartialSuffixTracking::requiresSuffix
        return split_type in ['P', 'D']

    def get_awb_suffixes(self, awb_number: str) -> list:
        """
        Get all suffixes for an AWB.

        Args:
            awb_number (str): AWB number

        Returns:
            list: List of suffix records
        """
        try:
            # Matches Laravel PartialSuffixTracking::getAwbSuffixes
            self.db_cursor.execute("""
                SELECT awb_number, manifest_id, flight_number, flight_date,
                       suffix_number, generated_suffix, split_type, created_at
                FROM partial_suffix_tracking
                WHERE awb_number = %s
                ORDER BY suffix_number
            """, (awb_number,))

            results = self.db_cursor.fetchall()

            # Convert to list of dictionaries
            suffixes = []
            for row in results:
                suffixes.append({
                    'awb_number': row[0],
                    'manifest_id': row[1],
                    'flight_number': row[2],
                    'flight_date': row[3],
                    'suffix_number': row[4],
                    'generated_suffix': row[5],
                    'split_type': row[6],
                    'created_at': row[7]
                })

            return suffixes

        except Exception as e:
            self.logger.error(f"Error getting AWB suffixes for {awb_number}: {str(e)}")
            return []
