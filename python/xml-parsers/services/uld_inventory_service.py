#!/usr/bin/env python3
"""
ULD Inventory Management Service.

This service provides comprehensive ULD inventory management capabilities including:
- Real-time inventory tracking
- ULD availability management
- Location-based inventory
- ULD utilization reporting
- Maintenance scheduling
- Inventory optimization
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum
import sys
import os

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.uld_service import ULDService, ULDStatus, ULDType


class InventoryStatus(Enum):
    """ULD inventory status enumeration."""
    AVAILABLE = "AVAILABLE"
    IN_USE = "IN_USE"
    MAINTENANCE = "MAINTENANCE"
    DAMAGED = "DAMAGED"
    RESERVED = "RESERVED"
    OUT_OF_SERVICE = "OUT_OF_SERVICE"


class ULDInventoryService:
    """Comprehensive ULD inventory management service."""
    
    def __init__(self, db_connection, config: Dict[str, Any]):
        """
        Initialize ULD inventory service.
        
        Args:
            db_connection: Database connection
            config: Configuration dictionary
        """
        self.db_connection = db_connection
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Initialize base ULD service
        self.uld_service = ULDService(db_connection, config)
        
        # Get inventory configuration
        self.inventory_config = config.get('uld_inventory', {})
        self.auto_reserve = self.inventory_config.get('auto_reserve_ulds', True)
        self.maintenance_threshold_days = self.inventory_config.get('maintenance_threshold_days', 30)
        self.utilization_threshold = self.inventory_config.get('utilization_threshold', 0.8)
    
    def get_inventory_summary(self, location: Optional[str] = None) -> Dict[str, Any]:
        """
        Get comprehensive inventory summary.
        
        Args:
            location: Filter by location (airport code)
            
        Returns:
            Dictionary containing inventory summary
        """
        try:
            cursor = self.db_connection.cursor()
            
            # Base query for inventory summary
            base_query = """
                SELECT 
                    uld_type,
                    status,
                    current_location,
                    COUNT(*) as count,
                    SUM(CASE WHEN is_damaged THEN 1 ELSE 0 END) as damaged_count,
                    SUM(CASE WHEN requires_maintenance THEN 1 ELSE 0 END) as maintenance_count,
                    AVG(tare_weight) as avg_tare_weight,
                    AVG(max_gross_weight) as avg_max_weight
                FROM uld_details 
                WHERE deleted_at IS NULL
            """
            
            params = []
            if location:
                base_query += " AND current_location = %s"
                params.append(location)
            
            base_query += " GROUP BY uld_type, status, current_location ORDER BY uld_type, status"
            
            cursor.execute(base_query, params)
            results = cursor.fetchall()
            
            # Process results into summary
            summary = {
                'total_ulds': 0,
                'by_type': {},
                'by_status': {},
                'by_location': {},
                'damaged_ulds': 0,
                'maintenance_required': 0,
                'utilization_rate': 0.0,
                'last_updated': datetime.now().isoformat()
            }
            
            for result in results:
                uld_type, status, current_location, count, damaged, maintenance, avg_tare, avg_max = result
                
                summary['total_ulds'] += count
                summary['damaged_ulds'] += damaged
                summary['maintenance_required'] += maintenance
                
                # By type
                if uld_type not in summary['by_type']:
                    summary['by_type'][uld_type] = {
                        'total': 0,
                        'available': 0,
                        'in_use': 0,
                        'maintenance': 0,
                        'avg_tare_weight': 0,
                        'avg_max_weight': 0
                    }
                
                summary['by_type'][uld_type]['total'] += count
                summary['by_type'][uld_type]['avg_tare_weight'] = float(avg_tare or 0)
                summary['by_type'][uld_type]['avg_max_weight'] = float(avg_max or 0)
                
                if status in ['AVAILABLE', 'PENDING']:
                    summary['by_type'][uld_type]['available'] += count
                elif status in ['LOADED', 'DEPARTED', 'ARRIVED']:
                    summary['by_type'][uld_type]['in_use'] += count
                elif status == 'MAINTENANCE':
                    summary['by_type'][uld_type]['maintenance'] += count
                
                # By status
                if status not in summary['by_status']:
                    summary['by_status'][status] = 0
                summary['by_status'][status] += count
                
                # By location
                if current_location:
                    if current_location not in summary['by_location']:
                        summary['by_location'][current_location] = {
                            'total': 0,
                            'available': 0,
                            'in_use': 0
                        }
                    
                    summary['by_location'][current_location]['total'] += count
                    if status in ['AVAILABLE', 'PENDING']:
                        summary['by_location'][current_location]['available'] += count
                    elif status in ['LOADED', 'DEPARTED', 'ARRIVED']:
                        summary['by_location'][current_location]['in_use'] += count
            
            # Calculate utilization rate
            total_available = summary['by_status'].get('AVAILABLE', 0) + summary['by_status'].get('PENDING', 0)
            total_in_use = (summary['by_status'].get('LOADED', 0) + 
                           summary['by_status'].get('DEPARTED', 0) + 
                           summary['by_status'].get('ARRIVED', 0))
            
            if total_available + total_in_use > 0:
                summary['utilization_rate'] = total_in_use / (total_available + total_in_use)
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Error getting inventory summary: {e}")
            return {}
    
    def get_available_ulds(self, location: str, uld_type: Optional[str] = None, 
                          required_count: int = 1) -> List[Dict[str, Any]]:
        """
        Get available ULDs for allocation.
        
        Args:
            location: Airport location code
            uld_type: Specific ULD type (optional)
            required_count: Number of ULDs required
            
        Returns:
            List of available ULD records
        """
        try:
            cursor = self.db_connection.cursor()
            
            query = """
                SELECT id, uld_id, uld_type, uld_owner, tare_weight, max_gross_weight,
                       volume_capacity, status, current_location, is_damaged, requires_maintenance
                FROM uld_details 
                WHERE deleted_at IS NULL 
                AND status IN ('AVAILABLE', 'PENDING')
                AND current_location = %s
                AND is_damaged = FALSE
                AND requires_maintenance = FALSE
            """
            
            params = [location]
            
            if uld_type:
                query += " AND uld_type = %s"
                params.append(uld_type)
            
            query += " ORDER BY updated_at ASC LIMIT %s"
            params.append(required_count)
            
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            available_ulds = []
            for result in results:
                available_ulds.append({
                    'id': result[0],
                    'uld_id': result[1],
                    'uld_type': result[2],
                    'uld_owner': result[3],
                    'tare_weight': float(result[4] or 0),
                    'max_gross_weight': float(result[5] or 0),
                    'volume_capacity': float(result[6] or 0),
                    'status': result[7],
                    'current_location': result[8],
                    'is_damaged': result[9],
                    'requires_maintenance': result[10]
                })
            
            return available_ulds
            
        except Exception as e:
            self.logger.error(f"Error getting available ULDs: {e}")
            return []
    
    def reserve_ulds(self, uld_ids: List[str], manifest_id: str, 
                    user_id: int, notes: Optional[str] = None) -> bool:
        """
        Reserve ULDs for a specific manifest.
        
        Args:
            uld_ids: List of ULD IDs to reserve
            manifest_id: Manifest ID for reservation
            user_id: User making the reservation
            notes: Optional reservation notes
            
        Returns:
            True if successful, False otherwise
        """
        try:
            cursor = self.db_connection.cursor()
            
            # Update ULD status to reserved
            for uld_id in uld_ids:
                cursor.execute("""
                    UPDATE uld_details SET
                        status = 'RESERVED',
                        manifest_id = %s,
                        updated_by = %s,
                        updated_at = NOW()
                    WHERE uld_id = %s AND status IN ('AVAILABLE', 'PENDING')
                """, (manifest_id, user_id, uld_id))
                
                if cursor.rowcount > 0:
                    # Track the reservation movement
                    cursor.execute("""
                        INSERT INTO uld_movements (
                            uld_id, manifest_id, movement_type, from_status, to_status,
                            movement_time, notes, recorded_by, created_at
                        ) VALUES (%s, %s, 'STATUS_CHANGE', 'AVAILABLE', 'RESERVED', 
                                 NOW(), %s, %s, NOW())
                    """, (uld_id, manifest_id, notes or f'Reserved for manifest {manifest_id}', user_id))
                    
                    self.logger.info(f"Reserved ULD {uld_id} for manifest {manifest_id}")
                else:
                    self.logger.warning(f"Could not reserve ULD {uld_id} - may not be available")
            
            self.db_connection.commit()
            return True
            
        except Exception as e:
            self.db_connection.rollback()
            self.logger.error(f"Error reserving ULDs: {e}")
            return False
    
    def release_ulds(self, uld_ids: List[str], user_id: int, 
                    notes: Optional[str] = None) -> bool:
        """
        Release reserved ULDs back to available status.
        
        Args:
            uld_ids: List of ULD IDs to release
            user_id: User releasing the ULDs
            notes: Optional release notes
            
        Returns:
            True if successful, False otherwise
        """
        try:
            cursor = self.db_connection.cursor()
            
            for uld_id in uld_ids:
                cursor.execute("""
                    UPDATE uld_details SET
                        status = 'AVAILABLE',
                        updated_by = %s,
                        updated_at = NOW()
                    WHERE uld_id = %s AND status = 'RESERVED'
                """, (user_id, uld_id))
                
                if cursor.rowcount > 0:
                    # Track the release movement
                    cursor.execute("""
                        INSERT INTO uld_movements (
                            uld_id, manifest_id, movement_type, from_status, to_status,
                            movement_time, notes, recorded_by, created_at
                        ) VALUES (%s, NULL, 'STATUS_CHANGE', 'RESERVED', 'AVAILABLE', 
                                 NOW(), %s, %s, NOW())
                    """, (uld_id, notes or f'Released ULD {uld_id}', user_id))
                    
                    self.logger.info(f"Released ULD {uld_id}")
            
            self.db_connection.commit()
            return True
            
        except Exception as e:
            self.db_connection.rollback()
            self.logger.error(f"Error releasing ULDs: {e}")
            return False
    
    def get_uld_utilization_report(self, start_date: datetime, end_date: datetime,
                                  location: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate ULD utilization report for a date range.
        
        Args:
            start_date: Report start date
            end_date: Report end date
            location: Filter by location (optional)
            
        Returns:
            Dictionary containing utilization report
        """
        try:
            cursor = self.db_connection.cursor()
            
            # Query for ULD movements in date range
            query = """
                SELECT 
                    ud.uld_id,
                    ud.uld_type,
                    ud.current_location,
                    COUNT(CASE WHEN um.to_status IN ('LOADED', 'DEPARTED') THEN 1 END) as usage_count,
                    MIN(um.movement_time) as first_use,
                    MAX(um.movement_time) as last_use,
                    AVG(EXTRACT(EPOCH FROM (
                        LEAD(um.movement_time) OVER (PARTITION BY ud.uld_id ORDER BY um.movement_time) - 
                        um.movement_time
                    )) / 3600) as avg_cycle_hours
                FROM uld_details ud
                LEFT JOIN uld_movements um ON ud.uld_id = um.uld_id
                WHERE um.movement_time BETWEEN %s AND %s
            """
            
            params = [start_date, end_date]
            
            if location:
                query += " AND ud.current_location = %s"
                params.append(location)
            
            query += """
                GROUP BY ud.uld_id, ud.uld_type, ud.current_location
                ORDER BY usage_count DESC
            """
            
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            # Process results
            report = {
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': (end_date - start_date).days
                },
                'location': location,
                'total_ulds': len(results),
                'utilization_stats': {
                    'high_utilization': 0,  # >80% usage
                    'medium_utilization': 0,  # 40-80% usage
                    'low_utilization': 0,  # <40% usage
                    'unused': 0
                },
                'by_type': {},
                'top_performers': [],
                'underutilized': []
            }
            
            total_days = (end_date - start_date).days or 1
            
            for result in results:
                uld_id, uld_type, current_location, usage_count, first_use, last_use, avg_cycle = result
                
                # Calculate utilization rate
                utilization_rate = min(usage_count / total_days, 1.0) if total_days > 0 else 0
                
                uld_data = {
                    'uld_id': uld_id,
                    'uld_type': uld_type,
                    'location': current_location,
                    'usage_count': usage_count,
                    'utilization_rate': utilization_rate,
                    'first_use': first_use.isoformat() if first_use else None,
                    'last_use': last_use.isoformat() if last_use else None,
                    'avg_cycle_hours': float(avg_cycle or 0)
                }
                
                # Categorize utilization
                if utilization_rate > 0.8:
                    report['utilization_stats']['high_utilization'] += 1
                    if len(report['top_performers']) < 10:
                        report['top_performers'].append(uld_data)
                elif utilization_rate > 0.4:
                    report['utilization_stats']['medium_utilization'] += 1
                elif utilization_rate > 0:
                    report['utilization_stats']['low_utilization'] += 1
                    if len(report['underutilized']) < 10:
                        report['underutilized'].append(uld_data)
                else:
                    report['utilization_stats']['unused'] += 1
                    if len(report['underutilized']) < 10:
                        report['underutilized'].append(uld_data)
                
                # By type statistics
                if uld_type not in report['by_type']:
                    report['by_type'][uld_type] = {
                        'count': 0,
                        'total_usage': 0,
                        'avg_utilization': 0
                    }
                
                report['by_type'][uld_type]['count'] += 1
                report['by_type'][uld_type]['total_usage'] += usage_count
                report['by_type'][uld_type]['avg_utilization'] += utilization_rate
            
            # Calculate averages for types
            for uld_type in report['by_type']:
                type_data = report['by_type'][uld_type]
                if type_data['count'] > 0:
                    type_data['avg_utilization'] /= type_data['count']
            
            return report
            
        except Exception as e:
            self.logger.error(f"Error generating utilization report: {e}")
            return {}
    
    def schedule_maintenance(self, uld_id: str, maintenance_type: str, 
                           scheduled_date: datetime, user_id: int,
                           notes: Optional[str] = None) -> bool:
        """
        Schedule ULD for maintenance.
        
        Args:
            uld_id: ULD identifier
            maintenance_type: Type of maintenance required
            scheduled_date: Scheduled maintenance date
            user_id: User scheduling maintenance
            notes: Optional maintenance notes
            
        Returns:
            True if successful, False otherwise
        """
        try:
            cursor = self.db_connection.cursor()
            
            # Update ULD status to maintenance
            cursor.execute("""
                UPDATE uld_details SET
                    status = 'MAINTENANCE',
                    requires_maintenance = TRUE,
                    notes = %s,
                    updated_by = %s,
                    updated_at = NOW()
                WHERE uld_id = %s
            """, (f"Scheduled for {maintenance_type} on {scheduled_date.date()}", user_id, uld_id))
            
            if cursor.rowcount > 0:
                # Track maintenance scheduling
                cursor.execute("""
                    INSERT INTO uld_movements (
                        uld_id, manifest_id, movement_type, to_status,
                        movement_time, notes, recorded_by, created_at
                    ) VALUES (%s, NULL, 'STATUS_CHANGE', 'MAINTENANCE', 
                             NOW(), %s, %s, NOW())
                """, (uld_id, f"Scheduled {maintenance_type}: {notes or ''}", user_id))
                
                self.db_connection.commit()
                self.logger.info(f"Scheduled ULD {uld_id} for {maintenance_type}")
                return True
            else:
                self.logger.warning(f"Could not schedule maintenance for ULD {uld_id}")
                return False
                
        except Exception as e:
            self.db_connection.rollback()
            self.logger.error(f"Error scheduling maintenance: {e}")
            return False
    
    def get_maintenance_schedule(self, location: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get ULDs requiring maintenance.
        
        Args:
            location: Filter by location (optional)
            
        Returns:
            List of ULDs requiring maintenance
        """
        try:
            cursor = self.db_connection.cursor()
            
            query = """
                SELECT uld_id, uld_type, current_location, status, 
                       requires_maintenance, is_damaged, notes, updated_at
                FROM uld_details 
                WHERE deleted_at IS NULL 
                AND (requires_maintenance = TRUE OR is_damaged = TRUE OR status = 'MAINTENANCE')
            """
            
            params = []
            if location:
                query += " AND current_location = %s"
                params.append(location)
            
            query += " ORDER BY updated_at ASC"
            
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            maintenance_list = []
            for result in results:
                maintenance_list.append({
                    'uld_id': result[0],
                    'uld_type': result[1],
                    'location': result[2],
                    'status': result[3],
                    'requires_maintenance': result[4],
                    'is_damaged': result[5],
                    'notes': result[6],
                    'last_updated': result[7].isoformat() if result[7] else None
                })
            
            return maintenance_list
            
        except Exception as e:
            self.logger.error(f"Error getting maintenance schedule: {e}")
            return []
