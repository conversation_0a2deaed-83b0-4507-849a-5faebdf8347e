#!/usr/bin/env python3
"""
Integrated Cargo XML Parser Orchestrator.

This is the main orchestrator for the two-stage cargo XML parser system that handles:
- XFWB (Master Waybill XML) - Phase 1: Declaration
- XFFM (Flight Manifest XML) - Phase 2: Reconciliation
- XFZB (House Waybill XML) - Integration with master waybills

The system implements:
1. Duplicate prevention using SHA-256 hashing
2. Orphan detection and eventual recovery
3. Partial shipment tracking with ULD splits
4. In-transit logic and branch-aware processing
5. Comprehensive logging and audit trails
"""

import logging
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    WATCHDOG_AVAILABLE = True
except ImportError:
    WATCHDOG_AVAILABLE = False

# Add the current directory to the path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.database import DB_CONFIG
from processors.xfwb_phase1_processor import XFWBPhase1Processor
from processors.xffm_phase2_processor import XFFMPhase2Processor
from processors.xfzb_integrated_processor import XFZBIntegratedProcessor
from utils.message_type_detector import MessageTypeDetector

try:
    import psycopg2
    import psycopg2.extras
except ImportError:
    print("Error: psycopg2 not installed. Please install it with: pip install psycopg2-binary")
    sys.exit(1)


class IntegratedCargoParser:
    """
    Main orchestrator for the integrated cargo XML parser system.

    Coordinates the two-stage processing workflow:
    1. XFWB Phase 1: Master waybill declarations with duplicate prevention
    2. XFFM Phase 2: Flight reconciliation with split handling
    3. XFZB Integration: House waybill processing
    """

    def __init__(self, branch_id=1, user_id=1, log_level=logging.INFO):
        """
        Initialize the integrated cargo parser.

        Args:
            branch_id (int): Current branch ID for in-transit logic
            user_id (int): Current user ID for audit trails
            log_level: Logging level
        """
        self.branch_id = branch_id
        self.user_id = user_id

        # Setup logging
        self.logger = self._setup_logging(log_level)

        # Initialize database connection
        self.db_connection = None
        self.db_cursor = None

        # Initialize processors
        self.xfwb_processor = None
        self.xffm_processor = None
        self.xfzb_processor = None

        # Initialize message type detector
        self.message_detector = MessageTypeDetector()

    def _setup_logging(self, log_level):
        """Setup logging configuration."""
        logger = logging.getLogger('IntegratedCargoParser')
        logger.setLevel(log_level)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def connect_database(self):
        """Establish database connection."""
        try:
            self.db_connection = psycopg2.connect(**DB_CONFIG)
            self.db_cursor = self.db_connection.cursor(cursor_factory=psycopg2.extras.DictCursor)

            # Initialize processors with database connection
            self.xfwb_processor = XFWBPhase1Processor(
                self.db_connection, self.db_cursor, self.branch_id, self.user_id, self.logger
            )
            self.xffm_processor = XFFMPhase2Processor(
                self.db_connection, self.db_cursor, self.branch_id, self.user_id, self.logger
            )
            self.xfzb_processor = XFZBIntegratedProcessor(
                self.db_connection, self.db_cursor, self.branch_id, self.user_id, self.logger
            )

            self.logger.info("Database connection established successfully")

        except Exception as e:
            self.logger.error(f"Failed to connect to database: {str(e)}")
            raise

    def disconnect_database(self):
        """Close database connection."""
        try:
            if self.db_cursor:
                self.db_cursor.close()
            if self.db_connection:
                self.db_connection.close()
            self.logger.info("Database connection closed")
        except Exception as e:
            self.logger.error(f"Error closing database connection: {str(e)}")

    def process_file(self, file_path: str) -> Dict[str, Any]:
        """
        Process a single XML file using the appropriate processor with enforced processing order.

        Args:
            file_path (str): Path to XML file

        Returns:
            dict: Processing result with statistics and status
        """
        if not os.path.exists(file_path):
            return {
                'success': False,
                'file_name': os.path.basename(file_path),
                'error': f"File not found: {file_path}"
            }

        try:
            # Detect message type
            message_type = self.message_detector.detect_from_file(file_path)

            if not message_type:
                return {
                    'success': False,
                    'file_name': os.path.basename(file_path),
                    'error': 'Could not detect message type'
                }

            # Convert to uppercase for consistency
            message_type = message_type.upper()
            self.logger.info(f"Processing {message_type} file: {file_path}")

            # CRITICAL FIX: Enforce processing order - XFFM must be processed before XFWB
            if message_type == 'XFWB':
                # Check if XFFM has been processed for this manifest first
                processing_order_check = self.check_processing_order_for_xfwb(file_path)
                if not processing_order_check['can_process']:
                    return {
                        'success': False,
                        'file_name': os.path.basename(file_path),
                        'error': processing_order_check['error'],
                        'processing_order_violation': True,
                        'required_xffm_manifest': processing_order_check.get('manifest_id')
                    }
                return self.xfwb_processor.process_xfwb_file(file_path)
            elif message_type == 'XFFM':
                # XFFM can always be processed (it establishes expected values)
                return self.xffm_processor.process_xffm_file(file_path)
            elif message_type == 'XFZB':
                return self.xfzb_processor.process_xfzb_file(file_path)
            else:
                return {
                    'success': False,
                    'file_name': os.path.basename(file_path),
                    'error': f'Unsupported message type: {message_type}'
                }

        except Exception as e:
            self.logger.error(f"Error processing file {file_path}: {str(e)}", exc_info=True)
            return {
                'success': False,
                'file_name': os.path.basename(file_path),
                'error': str(e)
            }

    def check_processing_order_for_xfwb(self, xfwb_file_path: str) -> Dict[str, Any]:
        """
        Check if XFWB can be processed by verifying XFFM has been processed first.

        Args:
            xfwb_file_path (str): Path to XFWB file

        Returns:
            dict: Processing order check result
        """
        try:
            # Extract manifest/flight information from XFWB file to check dependencies
            from extractors.xfwb_extractor import XFWBExtractor

            xfwb_extractor = XFWBExtractor(self.logger)
            xfwb_data = xfwb_extractor.extract_from_file(xfwb_file_path)

            if not xfwb_data or not xfwb_data.get('success'):
                return {
                    'can_process': False,
                    'error': 'Could not extract XFWB data to check processing order'
                }

            # Get AWB number and related flight information
            master_waybill = xfwb_data.get('master_waybill', {})
            awb_number = master_waybill.get('awb_number')

            if not awb_number:
                return {
                    'can_process': False,
                    'error': 'AWB number not found in XFWB file'
                }

            # Check if this AWB has been processed by XFFM (has expected totals from manifest)
            self.db_cursor.execute("""
                SELECT manifest_id, total_pieces_expected, status
                FROM master_waybills
                WHERE awb_number = %s
                AND total_pieces_expected IS NOT NULL
                AND total_pieces_expected > 0
                ORDER BY created_at DESC
                LIMIT 1
            """, (awb_number,))

            xffm_result = self.db_cursor.fetchone()

            if xffm_result:
                # XFFM has been processed for this AWB - XFWB can proceed
                manifest_id = xffm_result[0]
                self.logger.info(f"XFWB processing order check passed: AWB {awb_number} has XFFM data from manifest {manifest_id}")
                return {
                    'can_process': True,
                    'manifest_id': manifest_id,
                    'message': f'XFFM already processed for AWB {awb_number}'
                }
            else:
                # No XFFM data found - XFWB cannot be processed yet
                self.logger.warning(f"XFWB processing order violation: AWB {awb_number} has no XFFM expected totals")
                return {
                    'can_process': False,
                    'error': f'XFWB cannot be processed for AWB {awb_number}: No XFFM manifest data found. XFFM must be processed first to establish expected totals.',
                    'awb_number': awb_number
                }

        except Exception as e:
            self.logger.error(f"Error checking processing order for XFWB: {str(e)}")
            return {
                'can_process': False,
                'error': f'Processing order check failed: {str(e)}'
            }

    def process_directory(self, directory_path: str, file_pattern: str = "*.xml") -> Dict[str, Any]:
        """
        Process all XML files in a directory.

        Args:
            directory_path (str): Path to directory containing XML files
            file_pattern (str): File pattern to match (default: "*.xml")

        Returns:
            dict: Overall processing results
        """
        start_time = time.time()
        results = {
            'success': True,
            'directory': directory_path,
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'file_results': [],
            'summary': {
                'xfwb_files': 0,
                'xffm_files': 0,
                'xfzb_files': 0,
                'total_awbs': 0,
                'total_partials': 0
            },
            'processing_time_ms': 0,
            'errors': []
        }

        try:
            # Find all XML files
            directory = Path(directory_path)
            xml_files = list(directory.glob(file_pattern))
            results['total_files'] = len(xml_files)

            if not xml_files:
                results['errors'].append(f"No XML files found in {directory_path}")
                return results

            self.logger.info(f"Found {len(xml_files)} XML files to process")

            # CRITICAL FIX: Sort files to process XFFM before XFWB
            sorted_files = self.sort_files_by_processing_order(xml_files)
            self.logger.info(f"Files sorted by processing order: XFFM first, then XFWB, then others")

            # Process each file in correct order
            for xml_file in sorted_files:
                file_result = self.process_file(str(xml_file))
                results['file_results'].append(file_result)

                if file_result['success']:
                    results['processed_files'] += 1

                    # Update summary statistics
                    if 'XFWB' in str(xml_file).upper():
                        results['summary']['xfwb_files'] += 1
                    elif 'XFFM' in str(xml_file).upper():
                        results['summary']['xffm_files'] += 1
                    elif 'XFZB' in str(xml_file).upper():
                        results['summary']['xfzb_files'] += 1

                    results['summary']['total_awbs'] += file_result.get('awb_count', 0)
                    results['summary']['total_partials'] += file_result.get('partial_count', 0)
                else:
                    results['failed_files'] += 1
                    results['errors'].append(f"{xml_file.name}: {file_result.get('error', 'Unknown error')}")

            # Calculate processing time
            processing_time_ms = int((time.time() - start_time) * 1000)
            results['processing_time_ms'] = processing_time_ms

            # Determine overall success
            results['success'] = results['failed_files'] == 0

            self.logger.info(f"Directory processing complete: {results['processed_files']}/{results['total_files']} files processed successfully")

        except Exception as e:
            results['success'] = False
            results['errors'].append(f"Error processing directory: {str(e)}")
            self.logger.error(f"Error processing directory {directory_path}: {str(e)}", exc_info=True)

        return results

    def sort_files_by_processing_order(self, xml_files: List) -> List:
        """
        Sort XML files to ensure XFFM files are processed before XFWB files.

        Args:
            xml_files (list): List of XML file paths

        Returns:
            list: Sorted list with XFFM first, then XFWB, then others
        """
        xffm_files = []
        xfwb_files = []
        other_files = []

        for xml_file in xml_files:
            try:
                message_type = self.message_detector.detect_from_file(str(xml_file))
                if message_type:
                    message_type = message_type.upper()
                    if message_type == 'XFFM':
                        xffm_files.append(xml_file)
                    elif message_type == 'XFWB':
                        xfwb_files.append(xml_file)
                    else:
                        other_files.append(xml_file)
                else:
                    other_files.append(xml_file)
            except Exception as e:
                self.logger.warning(f"Could not detect message type for {xml_file}: {str(e)}")
                other_files.append(xml_file)

        # Sort each category by filename for consistent processing
        xffm_files.sort(key=lambda x: str(x))
        xfwb_files.sort(key=lambda x: str(x))
        other_files.sort(key=lambda x: str(x))

        # Return in processing order: XFFM first, then XFWB, then others
        sorted_files = xffm_files + xfwb_files + other_files

        self.logger.info(f"File processing order: {len(xffm_files)} XFFM files, {len(xfwb_files)} XFWB files, {len(other_files)} other files")

        return sorted_files

    def extract_data_only(self, file_path: str) -> Dict[str, Any]:
        """
        Extract data from XML file without processing (for frontend integration).

        Args:
            file_path (str): Path to XML file

        Returns:
            dict: Extraction result
        """
        if not os.path.exists(file_path):
            return {
                'success': False,
                'file_name': os.path.basename(file_path),
                'error': f"File not found: {file_path}"
            }

        try:
            # Detect message type
            message_type = self.message_detector.detect_from_file(file_path)

            if not message_type:
                return {
                    'success': False,
                    'file_name': os.path.basename(file_path),
                    'error': 'Could not detect message type'
                }

            # Convert to uppercase for consistency
            message_type = message_type.upper()
            self.logger.info(f"Extracting data from {message_type} file: {file_path}")

            # Route to appropriate processor for extraction
            if message_type == 'XFFM':
                extractor = self.xffm_processor.extractor
            elif message_type == 'XFWB':
                extractor = self.xfwb_processor.extractor
            elif message_type == 'XFZB':
                extractor = self.xfzb_processor.extractor
            else:
                return {
                    'success': False,
                    'file_name': os.path.basename(file_path),
                    'error': f'Unsupported message type: {message_type}'
                }

            result = extractor.extract_from_file(file_path)

            if result['success']:
                return {
                    'success': True,
                    'message_type': message_type,
                    'file_name': os.path.basename(file_path),
                    'data': result['data']
                }
            else:
                return {
                    'success': False,
                    'file_name': os.path.basename(file_path),
                    'error': result.get('error', 'Extraction failed')
                }

        except Exception as e:
            self.logger.error(f"Error extracting data from file {file_path}: {str(e)}", exc_info=True)
            return {
                'success': False,
                'file_name': os.path.basename(file_path),
                'error': str(e)
            }

    def get_processing_statistics(self, days: int = 7) -> Dict[str, Any]:
        """
        Get enhanced processing statistics from the database.

        Args:
            days (int): Number of days to look back

        Returns:
            dict: Enhanced processing statistics
        """
        try:
            stats = {}

            # Get enhanced processing status statistics
            self.db_cursor.execute("""
                SELECT processing_status, COUNT(*) as count
                FROM master_waybills
                WHERE branch_id = %s
                GROUP BY processing_status
                ORDER BY processing_status
            """, (self.branch_id,))

            processing_status_stats = self.db_cursor.fetchall()
            stats['processing_status'] = {
                'master_waybills': {row[0]: row[1] for row in processing_status_stats}
            }

            # Get recent processing activity from enhanced logs
            self.db_cursor.execute("""
                SELECT message_type, status, COUNT(*) as count,
                       SUM(awbs_processed) as total_awbs,
                       SUM(ulds_processed) as total_ulds,
                       AVG(duration_seconds) as avg_duration
                FROM processing_logs
                WHERE started_at >= NOW() - INTERVAL '%s days'
                AND branch_id = %s
                GROUP BY message_type, status
                ORDER BY message_type, status
            """, (days, self.branch_id))

            recent_activity = self.db_cursor.fetchall()
            stats['recent_activity'] = [dict(row) for row in recent_activity]

            # Get duplicate prevention statistics
            self.db_cursor.execute("""
                SELECT message_type, COUNT(*) as files_processed,
                       SUM(attempt_count) as total_attempts,
                       COUNT(CASE WHEN attempt_count > 1 THEN 1 END) as duplicates_prevented
                FROM duplicate_files
                WHERE first_processed_at >= NOW() - INTERVAL '%s days'
                AND branch_id = %s
                GROUP BY message_type
                ORDER BY message_type
            """, (days, self.branch_id))

            duplicate_stats = self.db_cursor.fetchall()
            stats['duplicate_prevention'] = [dict(row) for row in duplicate_stats]

            # Get in-transit statistics
            self.db_cursor.execute("""
                SELECT
                    COUNT(*) as total_master_awbs,
                    SUM(CASE WHEN in_transit = TRUE THEN 1 ELSE 0 END) as in_transit_awbs,
                    SUM(CASE WHEN is_partial = TRUE THEN 1 ELSE 0 END) as partial_awbs
                FROM master_waybills
                WHERE branch_id = %s
            """, (self.branch_id,))

            master_stats = self.db_cursor.fetchone()
            stats['master_waybills'] = dict(master_stats) if master_stats else {}

            # Get partial waybill statistics with enhanced processing status
            self.db_cursor.execute("""
                SELECT processing_status, COUNT(*) as count
                FROM partial_waybills
                WHERE branch_id = %s
                GROUP BY processing_status
                ORDER BY processing_status
            """, (self.branch_id,))

            partial_status_stats = self.db_cursor.fetchall()
            stats['partial_waybills'] = {
                'by_processing_status': {row[0]: row[1] for row in partial_status_stats}
            }

            return stats

        except Exception as e:
            self.logger.error(f"Error getting processing statistics: {str(e)}")
            return {'error': str(e)}

    def __enter__(self):
        """Context manager entry."""
        self.connect_database()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect_database()


class XMLFileHandler(FileSystemEventHandler):
    """File system event handler for XML files."""

    def __init__(self, branch_id, user_id, log_level):
        self.branch_id = branch_id
        self.user_id = user_id
        self.log_level = log_level
        self.logger = logging.getLogger('XMLFileHandler')

    def on_created(self, event):
        """Handle file creation events."""
        if not event.is_directory and event.src_path.lower().endswith('.xml'):
            self.logger.info(f"New XML file detected: {event.src_path}")
            self.process_file(event.src_path)

    def on_moved(self, event):
        """Handle file move events."""
        if not event.is_directory and event.dest_path.lower().endswith('.xml'):
            self.logger.info(f"XML file moved to: {event.dest_path}")
            self.process_file(event.dest_path)

    def process_file(self, file_path):
        """Process a detected XML file."""
        try:
            # Wait a moment for file to be fully written
            time.sleep(1)

            with IntegratedCargoParser(self.branch_id, self.user_id, self.log_level) as parser:
                result = parser.process_file(file_path)

                if result['success']:
                    self.logger.info(f"Successfully processed {file_path}: "
                                   f"{result.get('awb_count', 0)} AWBs, "
                                   f"{result.get('partial_count', 0)} partials")
                else:
                    self.logger.error(f"Failed to process {file_path}: {result.get('error', 'Unknown error')}")

        except Exception as e:
            self.logger.error(f"Error processing file {file_path}: {str(e)}")


def start_directory_monitoring(directory_path, branch_id=1, user_id=1, log_level=logging.INFO):
    """
    Start monitoring a directory for new XML files.

    Args:
        directory_path (str): Directory to monitor
        branch_id (int): Branch ID for processing
        user_id (int): User ID for processing
        log_level: Logging level
    """
    if not WATCHDOG_AVAILABLE:
        print("Error: watchdog package not installed. Please install it with: pip install watchdog")
        sys.exit(1)

    # Setup logging
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    logger = logging.getLogger('DirectoryMonitor')
    logger.info(f"Starting directory monitoring: {directory_path}")

    # Create event handler and observer
    event_handler = XMLFileHandler(branch_id, user_id, log_level)
    observer = Observer()
    observer.schedule(event_handler, directory_path, recursive=False)

    # Start monitoring
    observer.start()
    logger.info(f"Monitoring started. Watching for XML files in: {directory_path}")
    logger.info("Press Ctrl+C to stop monitoring...")

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Stopping directory monitoring...")
        observer.stop()

    observer.join()
    logger.info("Directory monitoring stopped.")


def show_processing_statistics(branch_id=1, log_level=logging.INFO):
    """
    Show processing statistics from the database.

    Args:
        branch_id (int): Branch ID for filtering statistics
        log_level: Logging level
    """
    # Setup logging
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    try:
        with IntegratedCargoParser(branch_id, 1, log_level) as parser:
            print("\n" + "="*60)
            print("ENHANCED CARGO XML PARSER - PROCESSING STATISTICS")
            print("="*60)
            print(f"Branch ID: {branch_id}")
            print(f"Period: Last 7 days")

            # Get enhanced statistics
            stats = parser.get_processing_statistics(7)

            # Processing status summary
            if 'processing_status' in stats:
                print("\n📊 PROCESSING STATUS SUMMARY:")
                for status, count in stats['processing_status']['master_waybills'].items():
                    print(f"   {status}: {count}")

            # Recent activity
            if 'recent_activity' in stats:
                print("\n📈 RECENT ACTIVITY:")
                for activity in stats['recent_activity']:
                    print(f"   {activity['message_type']} {activity['status']}: "
                         f"{activity['count']} files, {activity['total_awbs']} AWBs")

            # Duplicate prevention
            if 'duplicate_prevention' in stats:
                print("\n🛡️  DUPLICATE PREVENTION:")
                for dup_stat in stats['duplicate_prevention']:
                    print(f"   {dup_stat['message_type']}: "
                         f"{dup_stat['duplicates_prevented']} duplicates prevented")

            # Master waybills summary
            if 'master_waybills' in stats:
                master = stats['master_waybills']
                print(f"\n� MASTER WAYBILLS SUMMARY:")
                print(f"   Total: {master.get('total_master_awbs', 0)}")
                print(f"   In Transit: {master.get('in_transit_awbs', 0)}")
                print(f"   Partial Shipments: {master.get('partial_awbs', 0)}")

            # Partial waybills summary
            if 'partial_waybills' in stats:
                partial = stats['partial_waybills']
                if 'by_processing_status' in partial:
                    print(f"\n📋 PARTIAL WAYBILLS BY STATUS:")
                    for status, count in partial['by_processing_status'].items():
                        print(f"   {status}: {count}")

            print("\n" + "="*60)
            print("🚀 Enhanced Cargo Manifest Processing System Active")
            print("✅ File & AWB duplicate prevention enabled")
            print("📊 Processing status lifecycle management active")
            print("� Partial suffix generation (one per flight arrival)")
            print("🌐 In-transit logic based on destination branches")
            print("="*60 + "\n")

    except Exception as e:
        print(f"Error retrieving statistics: {str(e)}")
        sys.exit(1)


def main():
    """
    Main function for command-line usage.
    """
    import argparse

    parser = argparse.ArgumentParser(description='Integrated Cargo XML Parser')
    parser.add_argument('input_path', nargs='?', help='Path to XML file or directory')
    parser.add_argument('--branch-id', type=int, default=1, help='Branch ID (default: 1)')
    parser.add_argument('--user-id', type=int, default=1, help='User ID (default: 1)')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose logging')
    parser.add_argument('--stats', action='store_true', help='Show processing statistics')
    parser.add_argument('--monitor', metavar='DIRECTORY', help='Monitor directory for new XML files')
    parser.add_argument('--format', choices=['pretty', 'json'], default='pretty', help='Output format (default: pretty)')
    parser.add_argument('--extract-only', action='store_true', help='Only extract data, do not process')

    args = parser.parse_args()

    # Set log level
    log_level = logging.DEBUG if args.verbose else logging.INFO

    # Handle stats mode
    if args.stats:
        show_processing_statistics(args.branch_id, log_level)
        return

    # Handle monitoring mode
    if args.monitor:
        if not os.path.exists(args.monitor):
            print(f"Error: Monitor directory not found: {args.monitor}")
            sys.exit(1)
        if not os.path.isdir(args.monitor):
            print(f"Error: Monitor path is not a directory: {args.monitor}")
            sys.exit(1)

        # Start directory monitoring
        start_directory_monitoring(args.monitor, args.branch_id, args.user_id, log_level)
        return

    # Validate input path for single file/directory processing
    if not args.input_path:
        print("Error: input_path is required when not using --monitor or --stats")
        sys.exit(1)

    if not os.path.exists(args.input_path):
        print(f"Error: Path not found: {args.input_path}")
        sys.exit(1)

    try:
        with IntegratedCargoParser(args.branch_id, args.user_id, log_level) as parser:

            if args.stats:
                # Show statistics
                stats = parser.get_processing_statistics()
                print("\n=== Processing Statistics ===")
                print(f"Recent 24h Activity: {stats.get('recent_24h', [])}")
                print(f"Master Waybills: {stats.get('master_waybills', {})}")
                print(f"Partial Waybills: {stats.get('partial_waybills', {})}")
                return

            # Process input
            if os.path.isfile(args.input_path):
                # Process single file
                if args.extract_only:
                    # Extract only mode
                    result = parser.extract_data_only(args.input_path)
                else:
                    # Full processing mode
                    result = parser.process_file(args.input_path)

                # Output in requested format
                if args.format == 'json':
                    import json
                    print(json.dumps(result, default=str))
                else:
                    # Pretty format (default)
                    print(f"\n=== Processing Result ===")
                    print(f"File: {result.get('file_name', 'Unknown')}")
                    print(f"Success: {result.get('success', False)}")
                    if result.get('success'):
                        print(f"AWBs Processed: {result.get('awb_count', 0)}")
                        print(f"Partials Created: {result.get('partial_count', 0)}")
                        print(f"Processing Time: {result.get('processing_time_ms', 0)}ms")
                        if result.get('warnings'):
                            print(f"Warnings: {result['warnings']}")
                    else:
                        print(f"Errors: {result.get('errors', [])}")

            elif os.path.isdir(args.input_path):
                # Process directory
                result = parser.process_directory(args.input_path)
                print(f"\n=== Directory Processing Result ===")
                print(f"Directory: {result.get('directory', 'Unknown')}")
                print(f"Total Files: {result.get('total_files', 0)}")
                print(f"Processed: {result.get('processed_files', 0)}")
                print(f"Failed: {result.get('failed_files', 0)}")
                print(f"Processing Time: {result.get('processing_time_ms', 0)}ms")
                print(f"Summary: {result.get('summary', {})}")
                if result.get('errors'):
                    print(f"Errors: {result['errors']}")
            else:
                print(f"Error: Path not found: {args.input_path}")
                sys.exit(1)

    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
