#!/usr/bin/env python3
"""
XML Parser Monitor Service
Monitors a directory for new XML files and processes them automatically.
"""

import os
import sys
import time
import logging
import argparse
import json
from datetime import datetime
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from run_parser import process_file

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("/var/log/xml-parser-monitor.log"),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('XMLParserMonitor')

class XMLFileHandler(FileSystemEventHandler):
    """Handler for XML file events."""
    
    def __init__(self, branch_id=1, user_id=1, processed_dir=None):
        self.branch_id = branch_id
        self.user_id = user_id
        self.processed_dir = processed_dir
        self.processing = set()  # Track files being processed
        
    def on_created(self, event):
        """Handle file creation events."""
        if not event.is_directory and event.src_path.upper().endswith('.XML'):
            # Wait a moment for file to be fully written
            time.sleep(2)
            self.process_xml_file(event.src_path)
    
    def on_moved(self, event):
        """Handle file move events."""
        if not event.is_directory and event.dest_path.upper().endswith('.XML'):
            # Wait a moment for file to be fully written
            time.sleep(2)
            self.process_xml_file(event.dest_path)
    
    def process_xml_file(self, file_path):
        """Process an XML file."""
        if file_path in self.processing:
            logger.info(f"File {file_path} is already being processed, skipping")
            return
            
        self.processing.add(file_path)
        
        try:
            logger.info(f"Processing new XML file: {file_path}")
            
            # Check if file exists and is readable
            if not os.path.isfile(file_path):
                logger.error(f"File not found: {file_path}")
                return
                
            # Process the file
            result = process_file(file_path, None, self.branch_id, self.user_id)
            
            if result.get('success', False):
                logger.info(f"Successfully processed {file_path}")
                
                # Move to processed directory if specified
                if self.processed_dir:
                    self.move_to_processed(file_path)
                    
            else:
                logger.error(f"Failed to process {file_path}: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            logger.error(f"Error processing {file_path}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
        finally:
            self.processing.discard(file_path)
    
    def move_to_processed(self, file_path):
        """Move processed file to processed directory."""
        try:
            if not os.path.exists(self.processed_dir):
                os.makedirs(self.processed_dir)
                
            filename = os.path.basename(file_path)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            new_filename = f"{timestamp}_{filename}"
            new_path = os.path.join(self.processed_dir, new_filename)
            
            os.rename(file_path, new_path)
            logger.info(f"Moved processed file to: {new_path}")
            
        except Exception as e:
            logger.error(f"Error moving file {file_path}: {str(e)}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Monitor directory for XML files and process them automatically.')
    
    parser.add_argument('-w', '--watch-dir', required=True,
                        help='Directory to watch for XML files')
    parser.add_argument('-p', '--processed-dir',
                        help='Directory to move processed files to')
    parser.add_argument('-b', '--branch-id', type=int, default=1,
                        help='Branch ID for processing')
    parser.add_argument('-u', '--user-id', type=int, default=1,
                        help='User ID for processing')
    parser.add_argument('-v', '--verbose', action='store_true',
                        help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate watch directory
    if not os.path.isdir(args.watch_dir):
        logger.error(f"Watch directory does not exist: {args.watch_dir}")
        return 1
    
    logger.info(f"Starting XML Parser Monitor")
    logger.info(f"Watching directory: {args.watch_dir}")
    logger.info(f"Branch ID: {args.branch_id}")
    logger.info(f"User ID: {args.user_id}")
    
    if args.processed_dir:
        logger.info(f"Processed files will be moved to: {args.processed_dir}")
    
    # Set up file system watcher
    event_handler = XMLFileHandler(
        branch_id=args.branch_id,
        user_id=args.user_id,
        processed_dir=args.processed_dir
    )
    
    observer = Observer()
    observer.schedule(event_handler, args.watch_dir, recursive=False)
    
    try:
        observer.start()
        logger.info("XML Parser Monitor started successfully")
        
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Received interrupt signal, stopping...")
        observer.stop()
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        observer.stop()
        return 1
    
    observer.join()
    logger.info("XML Parser Monitor stopped")
    return 0

if __name__ == '__main__':
    sys.exit(main())
