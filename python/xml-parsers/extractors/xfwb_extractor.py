#!/usr/bin/env python3
"""
XFWB data extractor.

This module provides functionality for extracting data from XFWB XML files.
"""

import sys
import os

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from extractors.base_extractor import BaseExtractor


class XFWBExtractor(BaseExtractor):
    """
    Extractor for XFWB (Master Air Waybill) XML data.

    This class extracts all relevant data from XFWB XML files including
    waybill information, party details, cargo details, and handling instructions.
    """

    def extract_from_file(self, file_path):
        """
        Extract data from XFWB XML file.

        Args:
            file_path (str): Path to the XML file

        Returns:
            dict: Extracted data with success flag
        """
        try:
            from lxml import etree

            # Parse the XML file
            parser = etree.XMLParser(remove_blank_text=True)
            tree = etree.parse(file_path, parser)
            root = tree.getroot()

            # Extract data using the main extract method
            extracted_data = self.extract(root)

            # Add metadata
            result = {
                'success': True,
                'master_waybill': extracted_data,
                'message_id': extracted_data.get('awb_number', 'UNKNOWN'),
                'manifest_id': None,  # XFWB doesn't have manifest ID
                'xml_content': etree.tostring(root, encoding='unicode', pretty_print=True)
            }

            return result

        except Exception as e:
            self.logger.error(f"Error extracting data from XFWB file {file_path}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'master_waybill': {},
                'message_id': None,
                'manifest_id': None
            }

    def extract(self, root_element):
        """
        Extract all data from XFWB XML.

        Args:
            root_element (Element): Root XML element.

        Returns:
            dict: Extracted waybill data.
        """
        data = {}

        # Extract basic waybill information
        data.update(self.extract_waybill_info(root_element))

        # Extract party information
        data.update(self.extract_party_info(root_element))

        # Extract cargo information
        data.update(self.extract_cargo_info(root_element))

        # Extract handling and service information
        data.update(self.extract_handling_info(root_element))

        # Extract payment and currency information
        data.update(self.extract_payment_info(root_element))

        # Extract flags and indicators
        data.update(self.extract_flags(root_element))

        return data

    def extract_waybill_info(self, root):
        """Extract basic waybill information."""
        data = {}

        # AWB number
        data['awb_number'] = self.extract_awb_number(root)

        # Type code
        data['type_code'] = self.extract_type_code(root)

        # Origin and destination
        data['origin_airport'] = self.extract_text(
            root, ".//*[local-name()='OriginLocation']/*[local-name()='ID']"
        )
        data['destination_airport'] = self.extract_text(
            root, ".//*[local-name()='FinalDestinationLocation']/*[local-name()='ID']"
        )

        return data

    def extract_awb_number(self, root):
        """Extract AWB number from various possible locations."""
        # Use the comprehensive AWB extraction method from base extractor
        awb_number = self.extract_awb_from_xml(root, message_type="XFWB")
        
        if not awb_number:
            self.logger.error("AWB number not found in XFWB file")
            # Additional debugging to help identify the structure of the XML
            try:
                # Log the root element name and first few child elements to help with debugging
                root_name = root.tag if hasattr(root, 'tag') else 'Unknown'
                self.logger.debug(f"XFWB XML root element: {root_name}")
                
                # Try to find any ID elements and log them for debugging
                id_elements = root.xpath(".//*[local-name()='ID']")
                if id_elements:
                    self.logger.debug(f"Found {len(id_elements)} ID elements in XFWB file")
                    for i, elem in enumerate(id_elements[:5]):  # Log first 5 only
                        self.logger.debug(f"ID element {i+1}: {elem.text if hasattr(elem, 'text') else 'No text'}")
                else:
                    self.logger.debug("No ID elements found in XFWB file")
            except Exception as e:
                self.logger.error(f"Error during XML structure debugging: {e}")
                
        return awb_number

    def extract_type_code(self, root):
        """Extract type code (740 or 741)."""
        # Try to find explicit type code
        type_code = self.extract_first_available(root, [
            ".//*[local-name()='MessageHeaderDocument']/*[local-name()='TypeCode']",
            ".//*[local-name()='BusinessHeaderDocument']/*[local-name()='TypeCode']"
        ])

        if type_code and type_code in ['740', '741']:
            return type_code

        # Determine type code based on content
        # Check for house consignments
        if self.element_exists(root, ".//*[local-name()='IncludedHouseConsignment']"):
            return '741'  # Master with houses

        # Check for consolidation indicators
        consolidation_notes = root.xpath(".//*[local-name()='IncludedHeaderNote']")
        for note in consolidation_notes:
            content_code = self.extract_text(note, ".//*[local-name()='ContentCode']")
            content = self.extract_text(note, ".//*[local-name()='Content']")
            if content_code == 'C' and content and 'CONSOLIDATION' in content.upper():
                return '741'  # Master with houses

        return '740'  # Default to direct waybill

    def extract_party_info(self, root):
        """Extract party information (shipper, consignee, agent, carrier)."""
        data = {}

        # Extract shipper information
        shipper_data = self.extract_party_data(root, 'ConsignorParty')
        if shipper_data:
            data['shipper_data'] = shipper_data

        # Extract consignee information
        consignee_data = self.extract_party_data(root, 'ConsigneeParty')
        if consignee_data:
            data['consignee_data'] = consignee_data

        # Extract agent information
        agent_data = self.extract_party_data(root, 'FreightForwarderParty')
        if agent_data:
            data['agent_data'] = agent_data

        # Extract carrier information
        carrier_data = self.extract_carrier_data(root)
        if carrier_data:
            data['carrier_data'] = carrier_data

        return data

    def extract_party_data(self, root, party_type):
        """Extract data for a specific party type."""
        party_xpath = f".//*[local-name()='{party_type}']"
        party_elements = root.xpath(party_xpath)

        if not party_elements:
            return None

        party = party_elements[0]

        data = {}

        # Party name
        data['name'] = self.extract_text(party, ".//*[local-name()='Name']", strip=True, upper=True)

        # Party ID/Code
        data['id'] = self.extract_text(party, ".//*[local-name()='ID']")

        # Address information
        address_data = self.extract_address_data(party)
        if address_data:
            data.update(address_data)

        return data

    def extract_address_data(self, party_element):
        """Extract address information from a party element."""
        data = {}

        # Address lines
        address_lines = self.extract_multiple_texts(
            party_element, ".//*[local-name()='PostalStructuredAddress']/*[local-name()='StreetName']"
        )
        if address_lines:
            data['address'] = '\n'.join(address_lines)

        # City
        data['city'] = self.extract_text(
            party_element, ".//*[local-name()='PostalStructuredAddress']/*[local-name()='CityName']"
        )

        # Country code
        data['country_code'] = self.extract_text(
            party_element, ".//*[local-name()='PostalStructuredAddress']/*[local-name()='CountryID']"
        )

        # Postal code
        data['postal_code'] = self.extract_text(
            party_element, ".//*[local-name()='PostalStructuredAddress']/*[local-name()='PostcodeCode']"
        )

        return data

    def extract_carrier_data(self, root):
        """Extract carrier information."""
        # Try different locations for carrier information
        carrier_elements = root.xpath(".//*[local-name()='CarrierParty']")
        if not carrier_elements:
            carrier_elements = root.xpath(".//*[local-name()='CarrierAssignedID']")

        if not carrier_elements:
            return None

        data = {}

        if carrier_elements[0].text:
            data['code'] = carrier_elements[0].text.strip()
            data['name'] = data['code']  # Use code as name if no separate name found

        return data

    def extract_cargo_info(self, root):
        """Extract cargo information."""
        data = {}

        # Pieces and weight
        data['total_pieces'] = self.extract_number(
            root, ".//*[local-name()='TotalPieceQuantity']", default=0
        )

        data['total_weight'] = self.extract_number(
            root, ".//*[local-name()='IncludedTareGrossWeightMeasure']", default=0.0, number_type=float
        )

        # Weight unit
        weight_elements = root.xpath(".//*[local-name()='IncludedTareGrossWeightMeasure']")
        if weight_elements:
            data['weight_unit'] = self.extract_attribute(
                root, ".//*[local-name()='IncludedTareGrossWeightMeasure']", 'unitCode', 'KGM'
            )
        else:
            data['weight_unit'] = 'KGM'

        # Volume information
        volume_data = self.extract_volume_info(root)
        if volume_data:
            data.update(volume_data)

        # Goods descriptions
        descriptions = self.extract_goods_descriptions(root)
        if descriptions:
            data['goods_descriptions'] = descriptions
            data['summary_description'] = self.create_summary_description(descriptions)

        return data

    def extract_volume_info(self, root):
        """Extract volume information."""
        data = {}

        # Try different XPaths for volume
        volume_xpaths = [
            ".//*[local-name()='GrossVolumeMeasure']",
            ".//*[local-name()='IncludedTareGrossVolumeMeasure']"
        ]

        for xpath in volume_xpaths:
            volume_elements = root.xpath(xpath)
            if volume_elements and volume_elements[0].text:
                data['gross_volume'] = self.extract_number(
                    root, xpath, default=None, number_type=float
                )
                data['volume_unit'] = self.extract_attribute(
                    root, xpath, 'unitCode', 'MC'
                )
                break

        return data

    def extract_goods_descriptions(self, root):
        """Extract goods descriptions."""
        descriptions = []

        # Try different XPaths for descriptions
        description_xpaths = [
            ".//*[local-name()='NatureIdentificationTransportCargo']/*[local-name()='Identification']",
            ".//*[local-name()='GoodsDescription']",
            ".//*[local-name()='IncludedHeaderNote'][.//*[local-name()='ContentCode']='P']/*[local-name()='Content']"
        ]

        for xpath in description_xpaths:
            texts = self.extract_multiple_texts(root, xpath, strip=True, upper=True)
            descriptions.extend(texts)

        # Remove duplicates while preserving order
        unique_descriptions = list(dict.fromkeys(descriptions))

        return unique_descriptions

    def create_summary_description(self, descriptions):
        """Create a summary description from goods descriptions."""
        if not descriptions:
            return None

        # Use the first description as summary, limited to 100 characters
        summary = descriptions[0][:100]
        if len(descriptions[0]) > 100:
            summary += '...'

        return summary

    def extract_handling_info(self, root):
        """Extract handling instructions and special service codes."""
        data = {}

        # Special handling codes
        shc_codes = self.extract_special_handling_codes(root)
        if shc_codes:
            data['special_handling_codes'] = shc_codes
            data['special_handling_code'] = shc_codes[0] if shc_codes else None

        return data

    def extract_special_handling_codes(self, root):
        """Extract special handling codes."""
        codes = []

        # Try different XPaths for handling codes
        code_xpaths = [
            ".//*[local-name()='SpecialHandlingCode']/*[local-name()='Code']",
            ".//*[local-name()='Service']/*[local-name()='ServiceCode']",
            ".//*[local-name()='ServiceCode']",
            ".//*[local-name()='HandlingSPHInstructions']/*[local-name()='DescriptionCode']"
        ]

        for xpath in code_xpaths:
            texts = self.extract_multiple_texts(root, xpath, strip=True, upper=True)
            # Limit to 3 characters for consistency
            codes.extend([text[:3] for text in texts if text])

        # Remove duplicates
        unique_codes = list(set(codes))

        return unique_codes

    def extract_payment_info(self, root):
        """Extract payment and currency information."""
        data = {}

        # Currency code
        data['currency_code'] = self.extract_text(
            root, ".//*[local-name()='ApplicableOriginCurrencyExchange']/*[local-name()='SourceCurrencyCode']"
        )

        # Prepaid/collect indicator
        indicator = self.extract_text(
            root, ".//*[local-name()='TotalChargePrepaidIndicator']"
        )
        data['prepaid_collect_indicator'] = indicator if indicator in ['P', 'C'] else 'P'

        return data

    def extract_flags(self, root):
        """Extract various flags and indicators."""
        data = {}

        # Mail shipment
        data['is_mail'] = self.is_mail_shipment(root)

        # Human remains
        data['is_human_remains'] = self.contains_human_remains(root)

        # Partial shipment
        data['is_partial'] = self.is_partial_shipment(root)

        return data

    def is_mail_shipment(self, root):
        """Check if this is a mail shipment."""
        # Check nature of goods for mail indicators
        nature_texts = self.extract_multiple_texts(
            root, ".//*[local-name()='NatureIdentificationTransportCargo']/*[local-name()='Identification']",
            upper=True
        )

        for text in nature_texts:
            if 'MAIL' in text or 'AO' in text:
                return True

        return False

    def contains_human_remains(self, root):
        """Check if shipment contains human remains."""
        # Check for human remains handling codes
        handling_codes = self.extract_special_handling_codes(root)
        human_remains_codes = ['HUM', 'HRM', 'SCI']

        return any(code in human_remains_codes for code in handling_codes)

    def is_partial_shipment(self, root):
        """Check if this is a partial shipment."""
        # Check for split sequence number other than 1
        split_seq = self.extract_text(root, ".//*[local-name()='SplitSequenceNumber']")
        if split_seq and split_seq.strip() != '1':
            return True

        # Check for partial indicators in descriptions
        descriptions = self.extract_goods_descriptions(root)
        partial_terms = ['PART SHIPMENT', 'PARTIAL SHIPMENT', 'SPLIT SHIPMENT']

        for desc in descriptions:
            if any(term in desc for term in partial_terms):
                return True

        return False
