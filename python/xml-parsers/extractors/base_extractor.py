#!/usr/bin/env python3
"""
Base extractor class for XML data extraction.

This module provides a base class for extracting data from XML elements
with common utility methods and error handling.
"""

import logging
from abc import ABC, abstractmethod
from lxml import etree


class BaseExtractor(ABC):
    """
    Base class for XML data extractors.
    
    This class provides common functionality for extracting data from XML elements
    including XPath utilities, text extraction, and error handling.
    """
    
    def __init__(self, logger=None):
        """
        Initialize the base extractor.
        
        Args:
            logger (logging.Logger): Logger instance. If None, creates a new one.
        """
        self.logger = logger or logging.getLogger(self.__class__.__name__)
    
    def extract_text(self, element, xpath, default=None, strip=True, upper=False):
        """
        Extract text from an XML element using XPath.
        
        Args:
            element (Element): XML element to search in.
            xpath (str): XPath expression.
            default: Default value if not found.
            strip (bool): Whether to strip whitespace.
            upper (bool): Whether to convert to uppercase.
            
        Returns:
            str: Extracted text or default value.
        """
        try:
            nodes = element.xpath(xpath)
            if nodes and nodes[0].text:
                text = nodes[0].text
                if strip:
                    text = text.strip()
                if upper:
                    text = text.upper()
                return text
        except Exception as e:
            self.logger.warning(f"Error extracting text with XPath '{xpath}': {e}")
        
        return default
    
    def extract_attribute(self, element, xpath, attribute, default=None):
        """
        Extract an attribute value from an XML element using XPath.
        
        Args:
            element (Element): XML element to search in.
            xpath (str): XPath expression.
            attribute (str): Attribute name.
            default: Default value if not found.
            
        Returns:
            str: Extracted attribute value or default value.
        """
        try:
            nodes = element.xpath(xpath)
            if nodes:
                return nodes[0].get(attribute, default)
        except Exception as e:
            self.logger.warning(f"Error extracting attribute '{attribute}' with XPath '{xpath}': {e}")
        
        return default
    
    def extract_multiple_texts(self, element, xpath, strip=True, upper=False):
        """
        Extract multiple text values from XML elements using XPath.
        
        Args:
            element (Element): XML element to search in.
            xpath (str): XPath expression.
            strip (bool): Whether to strip whitespace.
            upper (bool): Whether to convert to uppercase.
            
        Returns:
            list: List of extracted text values.
        """
        texts = []
        try:
            nodes = element.xpath(xpath)
            for node in nodes:
                if node.text:
                    text = node.text
                    if strip:
                        text = text.strip()
                    if upper:
                        text = text.upper()
                    texts.append(text)
        except Exception as e:
            self.logger.warning(f"Error extracting multiple texts with XPath '{xpath}': {e}")
        
        return texts
    
    def extract_number(self, element, xpath, default=0, number_type=int):
        """
        Extract a numeric value from an XML element using XPath.
        
        Args:
            element (Element): XML element to search in.
            xpath (str): XPath expression.
            default: Default value if not found or invalid.
            number_type (type): Type to convert to (int, float, etc.).
            
        Returns:
            Number: Extracted numeric value or default value.
        """
        try:
            text = self.extract_text(element, xpath)
            if text:
                return number_type(text)
        except (ValueError, TypeError) as e:
            self.logger.warning(f"Error converting '{text}' to {number_type.__name__}: {e}")
        except Exception as e:
            self.logger.warning(f"Error extracting number with XPath '{xpath}': {e}")
        
        return default
    
    def extract_boolean(self, element, xpath, default=False, true_values=None):
        """
        Extract a boolean value from an XML element using XPath.
        
        Args:
            element (Element): XML element to search in.
            xpath (str): XPath expression.
            default (bool): Default value if not found.
            true_values (list): List of values that should be considered True.
            
        Returns:
            bool: Extracted boolean value or default value.
        """
        if true_values is None:
            true_values = ['true', 'yes', '1', 'y', 'on']
        
        try:
            text = self.extract_text(element, xpath, strip=True, upper=True)
            if text:
                return text.lower() in [v.lower() for v in true_values]
        except Exception as e:
            self.logger.warning(f"Error extracting boolean with XPath '{xpath}': {e}")
        
        return default
    
    def element_exists(self, element, xpath):
        """
        Check if an element exists using XPath.
        
        Args:
            element (Element): XML element to search in.
            xpath (str): XPath expression.
            
        Returns:
            bool: True if element exists, False otherwise.
        """
        try:
            nodes = element.xpath(xpath)
            return len(nodes) > 0
        except Exception as e:
            self.logger.warning(f"Error checking element existence with XPath '{xpath}': {e}")
            return False
    
    def extract_first_available(self, element, xpaths, default=None, **kwargs):
        """
        Extract text from the first available XPath that returns a result.
        
        Args:
            element (Element): XML element to search in.
            xpaths (list): List of XPath expressions to try.
            default: Default value if none found.
            **kwargs: Additional arguments for extract_text.
            
        Returns:
            str: Extracted text or default value.
        """
        for xpath in xpaths:
            result = self.extract_text(element, xpath, default=None, **kwargs)
            if result is not None:
                return result
        
        return default
        
    def extract_awb_from_xml(self, element, message_type=None):
        """
        Extract AWB number from XML using multiple strategies.
        
        This method tries various common XPath patterns for AWB numbers
        across different IATA Cargo-XML message types.
        
        Args:
            element (Element): XML element to search in.
            message_type (str): Optional message type hint (XFWB, XFFM, etc.)
            
        Returns:
            str: Extracted AWB number or None if not found.
        """
        # Common XPaths for AWB numbers across message types
        common_xpaths = [
            ".//*[local-name()='MessageHeaderDocument']/*[local-name()='ID']",
            ".//*[local-name()='BusinessHeaderDocument']/*[local-name()='ID']",
            ".//*[local-name()='TransportContractDocument']/*[local-name()='ID']",
            ".//*[local-name()='MasterConsignment']/*[local-name()='ID']",
            ".//*[local-name()='AssociatedConsignmentItem']/*[local-name()='ID']",
            ".//*[local-name()='MainCarriageTransportMovement']/*[local-name()='AssociatedTransportDocument']/*[local-name()='ID']",
            ".//*[local-name()='IncludedHouseConsignment']/*[local-name()='ID']",
            ".//*[local-name()='AssociatedMasterConsignment']/*[local-name()='ID']",
            # Add more specific XPaths for testing
            "//ID"
        ]
        
        # Try common XPaths first
        awb = self.extract_first_available(element, common_xpaths)
        if awb:
            return awb
            
        # If still not found, try a more aggressive approach with wildcard search
        # This is more expensive but can find AWBs in non-standard locations
        try:
            # Look for any ID element that contains a pattern like XXX-XXXXXXXX
            import re
            for id_elem in element.xpath(".//*[local-name()='ID']"):
                if id_elem.text and re.match(r'\d{3}-\d{6,8}', id_elem.text.strip()):
                    return id_elem.text.strip()
                    
            # For test XML that might not have namespaces
            for id_elem in element.xpath("//ID"):
                if id_elem.text and re.match(r'\d{3}-\d{6,8}', id_elem.text.strip()):
                    return id_elem.text.strip()
        except Exception as e:
            self.logger.warning(f"Error in wildcard AWB search: {e}")
            
        return None
    
    @abstractmethod
    def extract(self, root_element):
        """
        Extract data from the root XML element. Must be implemented by subclasses.
        
        Args:
            root_element (Element): Root XML element.
            
        Returns:
            dict: Extracted data.
        """
        pass
