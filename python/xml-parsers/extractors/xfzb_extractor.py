#!/usr/bin/env python3
"""
XFZB data extractor.

This module provides functionality for extracting data from XFZB XML files.
"""

import sys
import os

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from extractors.base_extractor import BaseExtractor


class XFZBExtractor(BaseExtractor):
    """
    Extractor for XFZB (House Air Waybill) XML data.

    This class extracts all relevant data from XFZB XML files including
    house waybill information, party details, cargo details, and handling instructions.
    """

    def extract_from_file(self, file_path):
        """
        Extract data from XFZB XML file.

        Args:
            file_path (str): Path to the XML file

        Returns:
            dict: Extracted data with success flag
        """
        try:
            from lxml import etree

            # Parse the XML file
            parser = etree.XMLParser(remove_blank_text=True)
            tree = etree.parse(file_path, parser)
            root = tree.getroot()

            # Extract data using the main extract method
            extracted_data = self.extract(root)

            # Add metadata
            result = {
                'success': True,
                'house_waybills': [extracted_data],  # XFZB typically contains one house waybill
                'message_id': extracted_data.get('hawb_number', 'UNKNOWN'),
                'manifest_id': None,  # XFZB doesn't have manifest ID
                'xml_content': etree.tostring(root, encoding='unicode', pretty_print=True)
            }

            return result

        except Exception as e:
            self.logger.error(f"Error extracting data from XFZB file {file_path}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'house_waybills': [],
                'message_id': None,
                'manifest_id': None
            }

    def extract(self, root_element):
        """
        Extract all data from XFZB XML.

        Args:
            root_element (Element): Root XML element.

        Returns:
            dict: Extracted house waybill data.
        """
        data = {}

        # Extract basic house waybill information
        data.update(self.extract_waybill_info(root_element))

        # Extract party information
        data.update(self.extract_party_info(root_element))

        # Extract cargo information
        data.update(self.extract_cargo_info(root_element))

        # Extract handling and service information
        data.update(self.extract_handling_info(root_element))

        # Extract flags and indicators
        data.update(self.extract_flags(root_element))

        return data

    def extract_waybill_info(self, root):
        """Extract basic house waybill information."""
        data = {}

        # House AWB number
        data['hawb_number'] = self.extract_hawb_number(root)

        # Master AWB number
        data['mawb_number'] = self.extract_mawb_number(root)

        # Type code (always 703 for house waybills)
        data['type_code'] = '703'

        # Origin and destination
        data['origin_airport'] = self.extract_text(
            root, ".//*[local-name()='OriginLocation']/*[local-name()='ID']"
        )
        data['destination_airport'] = self.extract_text(
            root, ".//*[local-name()='FinalDestinationLocation']/*[local-name()='ID']"
        )

        return data

    def extract_hawb_number(self, root):
        """Extract house AWB number from various possible locations."""
        xpaths = [
            ".//*[local-name()='MessageHeaderDocument']/*[local-name()='ID']",
            ".//*[local-name()='BusinessHeaderDocument']/*[local-name()='ID']",
            ".//*[local-name()='HouseConsignment']/*[local-name()='ID']"
        ]
        return self.extract_first_available(root, xpaths)

    def extract_mawb_number(self, root):
        """Extract master AWB number."""
        xpaths = [
            ".//*[local-name()='AssociatedMasterConsignment']/*[local-name()='ID']",
            ".//*[local-name()='MasterConsignment']/*[local-name()='ID']",
            ".//*[local-name()='TransportContractDocument']/*[local-name()='ID']"
        ]
        return self.extract_first_available(root, xpaths)

    def extract_party_info(self, root):
        """Extract party information (shipper, consignee)."""
        data = {}

        # Extract shipper information
        shipper_data = self.extract_party_data(root, 'ConsignorParty')
        if shipper_data:
            data['shipper_data'] = shipper_data

        # Extract consignee information
        consignee_data = self.extract_party_data(root, 'ConsigneeParty')
        if consignee_data:
            data['consignee_data'] = consignee_data

        return data

    def extract_party_data(self, root, party_type):
        """Extract data for a specific party type."""
        party_xpath = f".//*[local-name()='{party_type}']"
        party_elements = root.xpath(party_xpath)

        if not party_elements:
            return None

        party = party_elements[0]

        data = {}

        # Party name
        data['name'] = self.extract_text(party, ".//*[local-name()='Name']", strip=True, upper=True)

        # Party ID/Code
        data['id'] = self.extract_text(party, ".//*[local-name()='ID']")

        # Address information
        address_data = self.extract_address_data(party)
        if address_data:
            data.update(address_data)

        return data

    def extract_address_data(self, party_element):
        """Extract address information from a party element."""
        data = {}

        # Address lines
        address_lines = self.extract_multiple_texts(
            party_element, ".//*[local-name()='PostalStructuredAddress']/*[local-name()='StreetName']"
        )
        if address_lines:
            data['address'] = '\n'.join(address_lines)

        # City
        data['city'] = self.extract_text(
            party_element, ".//*[local-name()='PostalStructuredAddress']/*[local-name()='CityName']"
        )

        # Country code
        data['country_code'] = self.extract_text(
            party_element, ".//*[local-name()='PostalStructuredAddress']/*[local-name()='CountryID']"
        )

        # Postal code
        data['postal_code'] = self.extract_text(
            party_element, ".//*[local-name()='PostalStructuredAddress']/*[local-name()='PostcodeCode']"
        )

        return data

    def extract_cargo_info(self, root):
        """Extract cargo information."""
        data = {}

        # Pieces and weight
        data['total_pieces'] = self.extract_number(
            root, ".//*[local-name()='TotalPieceQuantity']", default=0
        )

        data['total_weight'] = self.extract_number(
            root, ".//*[local-name()='IncludedTareGrossWeightMeasure']", default=0.0, number_type=float
        )

        # Weight unit
        weight_elements = root.xpath(".//*[local-name()='IncludedTareGrossWeightMeasure']")
        if weight_elements:
            data['weight_unit'] = self.extract_attribute(
                root, ".//*[local-name()='IncludedTareGrossWeightMeasure']", 'unitCode', 'KGM'
            )
        else:
            data['weight_unit'] = 'KGM'

        # Goods description
        description = self.extract_goods_description(root)
        if description:
            data['description'] = description

        return data

    def extract_goods_description(self, root):
        """Extract goods description."""
        # Try different XPaths for description
        description_xpaths = [
            ".//*[local-name()='NatureIdentificationTransportCargo']/*[local-name()='Identification']",
            ".//*[local-name()='GoodsDescription']",
            ".//*[local-name()='IncludedHeaderNote'][.//*[local-name()='ContentCode']='P']/*[local-name()='Content']"
        ]

        for xpath in description_xpaths:
            description = self.extract_text(root, xpath, strip=True, upper=True)
            if description:
                return description

        return None

    def extract_handling_info(self, root):
        """Extract handling instructions and special service codes."""
        data = {}

        # Special handling code (single code for house waybills)
        shc_code = self.extract_special_handling_code(root)
        if shc_code:
            data['special_handling_code'] = shc_code

        return data

    def extract_special_handling_code(self, root):
        """Extract special handling code."""
        # Try different XPaths for handling codes
        code_xpaths = [
            ".//*[local-name()='SpecialHandlingCode']/*[local-name()='Code']",
            ".//*[local-name()='Service']/*[local-name()='ServiceCode']",
            ".//*[local-name()='ServiceCode']",
            ".//*[local-name()='HandlingSPHInstructions']/*[local-name()='DescriptionCode']"
        ]

        for xpath in code_xpaths:
            code = self.extract_text(root, xpath, strip=True, upper=True)
            if code:
                # Limit to 3 characters for consistency
                return code[:3]

        return None

    def extract_flags(self, root):
        """Extract various flags and indicators."""
        data = {}

        # Mail shipment
        data['is_mail'] = self.is_mail_shipment(root)

        # Human remains
        data['is_human_remains'] = self.contains_human_remains(root)

        # Partial shipment (house waybills are typically not partial)
        data['is_partial'] = False

        return data

    def is_mail_shipment(self, root):
        """Check if this is a mail shipment."""
        # Check nature of goods for mail indicators
        description = self.extract_goods_description(root)
        if description and ('MAIL' in description or 'AO' in description):
            return True

        return False

    def contains_human_remains(self, root):
        """Check if shipment contains human remains."""
        # Check for human remains handling code
        handling_code = self.extract_special_handling_code(root)
        human_remains_codes = ['HUM', 'HRM', 'SCI']

        return handling_code in human_remains_codes if handling_code else False
