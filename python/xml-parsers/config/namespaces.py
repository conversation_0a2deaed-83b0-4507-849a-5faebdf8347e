#!/usr/bin/env python3
"""
XML namespace configurations.
"""

# Define namespaces for different XML types
NAMESPACES = {
    'xffm': {
        'rsm': 'iata:flightmanifest:1',
        'ram': 'iata:datamodel:3',
        'ns0': 'iata:flightmanifest:1',
        'ns1': 'iata:datamodel:3'
    },
    'xffm_alt': {
        'rsm': 'http://www.iata.org/IATA/2015/00/2018.2/IATA_AirShippingMessageStructure',
        'ram': 'http://www.iata.org/IATA/2015/00/2018.2/IATA_AirShippingMessageStructure',
        'ns0': 'iata:flightmanifest:1',
        'ns1': 'iata:datamodel:4',
        'ram': 'iata:datamodel:4'
    },
    'xffm_alt2': {
        'rsm': 'iata:flightmanifest:1',
        'ram': 'iata:datamodel:4',
        'ns0': 'iata:flightmanifest:1',
        'ns1': 'iata:datamodel:4'
    },
    'xfwb': {
        'rsm': 'iata:waybill:1',
        'ram': 'iata:datamodel:3',
        'ns0': 'iata:waybill:1',
        'ns1': 'iata:datamodel:3'
    },
    'xfwb_alt': {
        'rsm': 'iata:waybill:1',
        'ram': 'iata:datamodel:3',
        'ns0': 'iata:waybill:1',
        'ns1': 'iata:datamodel:3'
    },
    'xfzb': {
        'rsm': 'iata:housewaybill:1',
        'ram': 'iata:datamodel:3',
        'ns0': 'iata:housewaybill:1',
        'ns1': 'iata:datamodel:3'
    },
    'xfzb_alt': {
        'rsm': 'iata:housewaybill:1',
        'ram': 'iata:datamodel:3',
        'ns0': 'iata:housewaybill:1',
        'ns1': 'iata:datamodel:3'
    },
    'xfhl': {
        'rsm': 'iata:houselist:1',
        'ram': 'iata:datamodel:3',
        'ns0': 'iata:houselist:1',
        'ns1': 'iata:datamodel:3'
    },
    'xfnm': {
        'rsm': 'iata:statusupdate:1',
        'ram': 'iata:datamodel:3',
        'ns0': 'iata:statusupdate:1',
        'ns1': 'iata:datamodel:3'
    },
    'xfpr': {
        'rsm': 'iata:pre-advice:1',
        'ram': 'iata:datamodel:3',
        'ns0': 'iata:pre-advice:1',
        'ns1': 'iata:datamodel:3'
    },
    'xfcc': {
        'rsm': 'iata:consignmentcomplete:1',
        'ram': 'iata:datamodel:3',
        'ns0': 'iata:consignmentcomplete:1',
        'ns1': 'iata:datamodel:3'
    },
    'xfwr': {
        'rsm': 'iata:warehousereceipt:1',
        'ram': 'iata:datamodel:3',
        'ns0': 'iata:warehousereceipt:1',
        'ns1': 'iata:datamodel:3'
    },
    'xfma': {
        'rsm': 'iata:manifestamendment:1',
        'ram': 'iata:datamodel:3',
        'ns0': 'iata:manifestamendment:1',
        'ns1': 'iata:datamodel:3'
    },
    'xfem': {
        'rsm': 'iata:emptyuld:1',
        'ram': 'iata:datamodel:3',
        'ns0': 'iata:emptyuld:1',
        'ns1': 'iata:datamodel:3'
    }
}
