#!/usr/bin/env python3
"""
Process XFWB XML files.
This script processes XFWB XML files and updates or inserts master waybills
while handling existing parties (consignees, shippers, agents, and carriers).
"""

import argparse
import cProfile
import io
import json
import logging
import os
import pstats
import sys
import time
from datetime import datetime

# Add the current directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from parsers.xfwb_parser import XFWBParser

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("xfwb_processor.log"),
        logging.StreamHandler(sys.stdout),
    ],
)

logger = logging.getLogger("XFWBProcessor")

# Enable profiling
ENABLE_PROFILING = True


def process_xfwb(xml_file, branch_id=1, user_id=1):
    """
    Process XFWB XML file and return structured data.

    Args:
        xml_file (str): Path to the XML file.
        branch_id (int): Branch ID.
        user_id (int): User ID.

    Returns:
        dict: Structured data from the XFWB file.
    """
    parser = None
    start_time = time.time()
    profiler = None

    if ENABLE_PROFILING:
        profiler = cProfile.Profile()
        profiler.enable()

    try:
        # Initialize the parser with user_id and branch_id
        logger.info(f"Starting to process XFWB file: {xml_file}")
        parser = XFWBParser(user_id=user_id, branch_id=branch_id)

        # Parse the file using modular architecture
        logger.info(f"Parsing XFWB file: {xml_file}")
        parse_start = time.time()
        result = parser.parse_file(xml_file)
        parse_end = time.time()
        logger.info(f"Parsing completed in {parse_end - parse_start:.2f} seconds")

        if not result["success"]:
            error_msg = "Failed to parse XFWB file"
            if result["errors"]:
                error_msg += f": {'; '.join(result['errors'])}"
            raise Exception(error_msg)

        waybill_data = result["data"]
        awb_number = waybill_data.get("awb_number", "Unknown")

        end_time = time.time()
        logger.info(f"Successfully processed XFWB file: {xml_file}")
        logger.info(f"Processed waybill with AWB number: {awb_number}")
        logger.info(f"AWB ID: {result['awb_id']}")
        logger.info(f"Total processing time: {end_time - start_time:.2f} seconds")

        # Log any warnings
        if result["warnings"]:
            logger.warning("Processing warnings:")
            for warning in result["warnings"]:
                logger.warning(f"  - {warning}")

        return {
            "success": True,
            "awb_number": awb_number,
            "awb_id": result["awb_id"],
            "origin_airport": waybill_data.get("origin_airport", "Unknown"),
            "destination_airport": waybill_data.get("destination_airport", "Unknown"),
            "total_pieces": waybill_data.get("total_pieces", 0),
            "total_weight": waybill_data.get("total_weight", 0),
            "shipper_name": waybill_data.get("shipper_data", {}).get("name", "Unknown"),
            "consignee_name": waybill_data.get("consignee_data", {}).get(
                "name", "Unknown"
            ),
            "carrier_code": waybill_data.get("carrier_data", {}).get("code", "Unknown"),
            "is_mail": waybill_data.get("is_mail", False),
            "is_human_remains": waybill_data.get("is_human_remains", False),
            "is_partial": waybill_data.get("is_partial", False),
            "processing_time": end_time - start_time,
            "warnings": result["warnings"],
        }
    except Exception as e:
        logger.error(f"Error processing XFWB file: {e}")
        import traceback

        logger.error(traceback.format_exc())
        raise
    finally:
        if parser:
            parser.close()

        if ENABLE_PROFILING and profiler:
            profiler.disable()
            s = io.StringIO()
            ps = pstats.Stats(profiler, stream=s).sort_stats("cumulative")
            ps.print_stats(30)  # Print top 30 time-consuming functions
            logger.info(f"Profiling results:\n{s.getvalue()}")


def process_file(file_path):
    """
    Process an XFWB XML file.

    Args:
        file_path (str): Path to the XML file.

    Returns:
        bool: True if processing was successful, False otherwise.
    """
    parser = None
    try:
        logger.info(f"Processing file: {file_path}")

        # Create parser
        parser = XFWBParser()

        # Parse file using modular architecture
        result = parser.parse_file(file_path)

        if result["success"]:
            awb_number = (
                result["data"].get("awb_number", "Unknown")
                if result["data"]
                else "Unknown"
            )
            logger.info(f"Successfully processed waybill: {awb_number}")
            return True
        else:
            logger.error(f"Failed to process file: {file_path}")
            if result["errors"]:
                for error in result["errors"]:
                    logger.error(f"  - {error}")
            return False
    except Exception as e:
        logger.error(f"Error processing file {file_path}: {e}")
        return False
    finally:
        if parser:
            parser.close()


def process_directory(directory_path):
    """
    Process all XFWB XML files in a directory.

    Args:
        directory_path (str): Path to the directory.

    Returns:
        tuple: (total_files, successful_files)
    """
    if not os.path.isdir(directory_path):
        logger.error(f"Directory not found: {directory_path}")
        return 0, 0

    total_files = 0
    successful_files = 0

    for filename in os.listdir(directory_path):
        if filename.upper().startswith("XFWB") and filename.upper().endswith(".XML"):
            file_path = os.path.join(directory_path, filename)
            total_files += 1

            if process_file(file_path):
                successful_files += 1

    return total_files, successful_files


def main():
    """Main function."""
    # Check if we're being called directly from the command line or from the web interface
    if len(sys.argv) == 2 and os.path.isfile(sys.argv[1]):
        # Called from the web interface with just the file path
        xml_file = sys.argv[1]
        branch_id = 1
        user_id = 1

        if not os.path.exists(xml_file):
            print(
                json.dumps(
                    {"success": False, "error": f"File {xml_file} does not exist"}
                )
            )
            return 1

        try:
            result = process_xfwb(xml_file, branch_id, user_id)
            print(json.dumps(result))
            return 0
        except Exception as e:
            print(json.dumps({"success": False, "error": str(e)}))
            return 1
    # Check if we're being called with three arguments (file path, user_id, branch_id)
    # This is the case when called from Laravel's XmlImportController
    elif len(sys.argv) == 4 and os.path.isfile(sys.argv[1]):
        xml_file = sys.argv[1]
        try:
            user_id = int(sys.argv[2])
            branch_id = int(sys.argv[3])
        except ValueError:
            print(
                json.dumps(
                    {
                        "success": False,
                        "error": "Branch ID and User ID must be integers",
                    }
                )
            )
            return 1

        if not os.path.exists(xml_file):
            print(
                json.dumps(
                    {"success": False, "error": f"File {xml_file} does not exist"}
                )
            )
            return 1

        try:
            result = process_xfwb(xml_file, branch_id, user_id)
            print(json.dumps(result))
            return 0
        except Exception as e:
            print(json.dumps({"success": False, "error": str(e)}))
            return 1
    else:
        # Called from the command line with arguments
        parser = argparse.ArgumentParser(description="Process XFWB XML files.")
        group = parser.add_mutually_exclusive_group(required=True)
        group.add_argument("-f", "--file", help="Path to an XFWB XML file")
        group.add_argument(
            "-d", "--directory", help="Path to a directory containing XFWB XML files"
        )
        parser.add_argument("-b", "--branch-id", type=int, default=1, help="Branch ID")
        parser.add_argument("-u", "--user-id", type=int, default=1, help="User ID")

        args = parser.parse_args()

        if args.file:
            if not os.path.isfile(args.file):
                logger.error(f"File not found: {args.file}")
                return 1

            try:
                result = process_xfwb(args.file, args.branch_id, args.user_id)
                print(json.dumps(result))
                return 0
            except Exception as e:
                logger.error(f"Error processing file: {e}")
                return 1

        elif args.directory:
            total_files, successful_files = process_directory(args.directory)

            logger.info(
                f"Processed {successful_files} out of {total_files} files successfully"
            )

            if total_files == 0:
                logger.warning("No XFWB XML files found in the directory")
                return 0

            if successful_files < total_files:
                logger.warning(
                    f"Failed to process {total_files - successful_files} files"
                )
                return 1

            return 0


if __name__ == "__main__":
    sys.exit(main())
