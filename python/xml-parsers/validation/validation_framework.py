#!/usr/bin/env python3
"""
Comprehensive Validation Framework for XML Parsers.

This module implements a three-tier validation system (errors, warnings, hints)
based on the improvement recommendations from the Java ONE Record converter.
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from enum import Enum


class ValidationLevel(Enum):
    """Validation message levels."""
    ERROR = "ERROR"
    WARNING = "WARNING"
    HINT = "HINT"


class ValidationMessage:
    """Individual validation message."""
    
    def __init__(self, code: str, message: str, level: ValidationLevel, 
                 field: Optional[str] = None, value: Optional[Any] = None):
        """
        Initialize validation message.
        
        Args:
            code: Validation code (e.g., 'MISSING_AWB', 'INVALID_FORMAT')
            message: Human-readable message
            level: Validation level (ERROR, WARNING, HINT)
            field: Field name that caused the validation issue
            value: Field value that caused the validation issue
        """
        self.code = code
        self.message = message
        self.level = level
        self.field = field
        self.value = value
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'code': self.code,
            'message': self.message,
            'level': self.level.value,
            'field': self.field,
            'value': str(self.value) if self.value is not None else None,
            'timestamp': self.timestamp.isoformat()
        }
    
    def __str__(self) -> str:
        """String representation."""
        field_info = f" (field: {self.field})" if self.field else ""
        return f"[{self.level.value}] {self.code}: {self.message}{field_info}"


class ValidationResult:
    """Container for validation results."""
    
    def __init__(self):
        """Initialize validation result."""
        self.errors: List[ValidationMessage] = []
        self.warnings: List[ValidationMessage] = []
        self.hints: List[ValidationMessage] = []
        self.start_time = datetime.now()
        self.end_time: Optional[datetime] = None
    
    def add_error(self, code: str, message: str, field: Optional[str] = None, 
                  value: Optional[Any] = None) -> None:
        """Add an error message."""
        self.errors.append(ValidationMessage(code, message, ValidationLevel.ERROR, field, value))
    
    def add_warning(self, code: str, message: str, field: Optional[str] = None, 
                    value: Optional[Any] = None) -> None:
        """Add a warning message."""
        self.warnings.append(ValidationMessage(code, message, ValidationLevel.WARNING, field, value))
    
    def add_hint(self, code: str, message: str, field: Optional[str] = None, 
                 value: Optional[Any] = None) -> None:
        """Add a hint message."""
        self.hints.append(ValidationMessage(code, message, ValidationLevel.HINT, field, value))
    
    def has_errors(self) -> bool:
        """Check if there are any errors."""
        return len(self.errors) > 0
    
    def has_warnings(self) -> bool:
        """Check if there are any warnings."""
        return len(self.warnings) > 0
    
    def has_hints(self) -> bool:
        """Check if there are any hints."""
        return len(self.hints) > 0
    
    def is_valid(self) -> bool:
        """Check if validation passed (no errors)."""
        return not self.has_errors()
    
    def get_total_count(self) -> int:
        """Get total number of validation messages."""
        return len(self.errors) + len(self.warnings) + len(self.hints)
    
    def finalize(self) -> None:
        """Mark validation as complete."""
        self.end_time = datetime.now()
    
    def get_duration_ms(self) -> Optional[int]:
        """Get validation duration in milliseconds."""
        if self.end_time:
            return int((self.end_time - self.start_time).total_seconds() * 1000)
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'errors': [msg.to_dict() for msg in self.errors],
            'warnings': [msg.to_dict() for msg in self.warnings],
            'hints': [msg.to_dict() for msg in self.hints],
            'summary': {
                'error_count': len(self.errors),
                'warning_count': len(self.warnings),
                'hint_count': len(self.hints),
                'total_count': self.get_total_count(),
                'is_valid': self.is_valid(),
                'duration_ms': self.get_duration_ms()
            },
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None
        }
    
    def get_summary(self) -> str:
        """Get a summary string of validation results."""
        duration = f" ({self.get_duration_ms()}ms)" if self.end_time else ""
        return (f"Validation Summary{duration}: "
                f"{len(self.errors)} errors, "
                f"{len(self.warnings)} warnings, "
                f"{len(self.hints)} hints")


class BaseValidator:
    """Base class for validators."""
    
    def __init__(self, validation_result: ValidationResult):
        """
        Initialize validator.
        
        Args:
            validation_result: ValidationResult instance to store results
        """
        self.validation_result = validation_result
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def validate(self, data: Dict[str, Any]) -> ValidationResult:
        """
        Validate data. Override in subclasses.
        
        Args:
            data: Data to validate
            
        Returns:
            ValidationResult: Validation results
        """
        raise NotImplementedError("Subclasses must implement validate method")


class AWBValidator(BaseValidator):
    """Validator for AWB (Air Waybill) data."""
    
    def validate(self, data: Dict[str, Any]) -> ValidationResult:
        """Validate AWB data."""
        self._validate_awb_number(data.get('awb_number'))
        self._validate_airports(data.get('origin_airport'), data.get('destination_airport'))
        self._validate_weight_pieces(data.get('total_weight'), data.get('total_pieces'))
        self._validate_parties(data)
        return self.validation_result
    
    def _validate_awb_number(self, awb_number: Optional[str]) -> None:
        """Validate AWB number format."""
        if not awb_number:
            self.validation_result.add_error('MISSING_AWB', 'AWB number is required', 'awb_number')
            return
        
        # Remove hyphens and spaces
        clean_awb = awb_number.replace('-', '').replace(' ', '')
        
        # Check length (should be 11 digits)
        if len(clean_awb) != 11:
            self.validation_result.add_error(
                'INVALID_AWB_LENGTH', 
                f'AWB number must be 11 digits, got {len(clean_awb)}',
                'awb_number', awb_number
            )
            return
        
        # Check if all characters are digits
        if not clean_awb.isdigit():
            self.validation_result.add_error(
                'INVALID_AWB_FORMAT', 
                'AWB number must contain only digits',
                'awb_number', awb_number
            )
            return
        
        # Validate check digit (simplified Luhn algorithm)
        if not self._validate_awb_check_digit(clean_awb):
            self.validation_result.add_warning(
                'INVALID_AWB_CHECK_DIGIT', 
                'AWB check digit validation failed',
                'awb_number', awb_number
            )
    
    def _validate_awb_check_digit(self, awb_number: str) -> bool:
        """Validate AWB check digit using simplified algorithm."""
        # This is a simplified check - implement proper IATA check digit algorithm
        try:
            check_digit = int(awb_number[-1])
            number_part = awb_number[:-1]
            calculated_check = sum(int(d) for d in number_part) % 7
            return check_digit == calculated_check
        except (ValueError, IndexError):
            return False
    
    def _validate_airports(self, origin: Optional[str], destination: Optional[str]) -> None:
        """Validate airport codes."""
        if not origin:
            self.validation_result.add_error('MISSING_ORIGIN', 'Origin airport is required', 'origin_airport')
        elif not self._is_valid_airport_code(origin):
            self.validation_result.add_error(
                'INVALID_ORIGIN_FORMAT', 
                'Origin airport must be 3-letter IATA code',
                'origin_airport', origin
            )
        
        if not destination:
            self.validation_result.add_error('MISSING_DESTINATION', 'Destination airport is required', 'destination_airport')
        elif not self._is_valid_airport_code(destination):
            self.validation_result.add_error(
                'INVALID_DESTINATION_FORMAT', 
                'Destination airport must be 3-letter IATA code',
                'destination_airport', destination
            )
        
        if origin and destination and origin == destination:
            self.validation_result.add_warning(
                'SAME_ORIGIN_DESTINATION', 
                'Origin and destination airports are the same',
                'origin_airport', origin
            )
    
    def _is_valid_airport_code(self, code: str) -> bool:
        """Check if airport code format is valid."""
        return len(code) == 3 and code.isalpha() and code.isupper()
    
    def _validate_weight_pieces(self, weight: Optional[float], pieces: Optional[int]) -> None:
        """Validate weight and pieces."""
        if weight is None:
            self.validation_result.add_error('MISSING_WEIGHT', 'Total weight is required', 'total_weight')
        elif weight <= 0:
            self.validation_result.add_error(
                'INVALID_WEIGHT', 
                'Total weight must be positive',
                'total_weight', weight
            )
        elif weight > 50000:  # 50 tons seems excessive for air cargo
            self.validation_result.add_warning(
                'EXCESSIVE_WEIGHT', 
                'Total weight seems unusually high',
                'total_weight', weight
            )
        
        if pieces is None:
            self.validation_result.add_error('MISSING_PIECES', 'Total pieces is required', 'total_pieces')
        elif pieces <= 0:
            self.validation_result.add_error(
                'INVALID_PIECES', 
                'Total pieces must be positive',
                'total_pieces', pieces
            )
        elif pieces > 10000:  # 10k pieces seems excessive
            self.validation_result.add_warning(
                'EXCESSIVE_PIECES', 
                'Total pieces seems unusually high',
                'total_pieces', pieces
            )
    
    def _validate_parties(self, data: Dict[str, Any]) -> None:
        """Validate party information."""
        shipper_name = data.get('shipper_name')
        consignee_name = data.get('consignee_name')
        
        if not shipper_name:
            self.validation_result.add_error('MISSING_SHIPPER', 'Shipper name is required', 'shipper_name')
        elif len(shipper_name.strip()) < 3:
            self.validation_result.add_warning(
                'SHORT_SHIPPER_NAME', 
                'Shipper name seems too short',
                'shipper_name', shipper_name
            )
        
        if not consignee_name:
            self.validation_result.add_error('MISSING_CONSIGNEE', 'Consignee name is required', 'consignee_name')
        elif len(consignee_name.strip()) < 3:
            self.validation_result.add_warning(
                'SHORT_CONSIGNEE_NAME', 
                'Consignee name seems too short',
                'consignee_name', consignee_name
            )
        
        if shipper_name and consignee_name and shipper_name.strip() == consignee_name.strip():
            self.validation_result.add_hint(
                'SAME_SHIPPER_CONSIGNEE', 
                'Shipper and consignee have the same name',
                'shipper_name', shipper_name
            )
