#!/usr/bin/env python3
"""
Data Quality Assessment System for XML Parsers.

This module provides comprehensive data quality assessment capabilities
including completeness, accuracy, and consistency scoring.
"""

import logging
from typing import Dict, Any, List, Tuple, Optional, Callable
from datetime import datetime
import re


class DataQualityMetrics:
    """Container for data quality metrics."""

    def __init__(self):
        """Initialize metrics."""
        self.overall_score: float = 0.0
        self.completeness_score: float = 0.0
        self.accuracy_score: float = 0.0
        self.consistency_score: float = 0.0
        self.field_scores: Dict[str, float] = {}
        self.missing_fields: List[str] = []
        self.invalid_fields: List[Dict[str, Any]] = []
        self.recommendations: List[str] = []
        self.assessment_time = datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'overall_score': round(self.overall_score, 3),
            'completeness_score': round(self.completeness_score, 3),
            'accuracy_score': round(self.accuracy_score, 3),
            'consistency_score': round(self.consistency_score, 3),
            'field_scores': {k: round(v, 3) for k, v in self.field_scores.items()},
            'missing_fields': self.missing_fields,
            'invalid_fields': self.invalid_fields,
            'recommendations': self.recommendations,
            'assessment_time': self.assessment_time.isoformat()
        }


class FieldValidator:
    """Field-specific validator with scoring."""

    def __init__(self, name: str, weight: float, validator_func: Callable[[Any], Tuple[bool, float]]):
        """
        Initialize field validator.

        Args:
            name: Field name
            weight: Weight for overall score calculation (0.0 to 1.0)
            validator_func: Function that returns (is_valid, score)
        """
        self.name = name
        self.weight = weight
        self.validator_func = validator_func


class DataQualityAssessment:
    """Comprehensive data quality assessment for cargo XML data."""

    def __init__(self):
        """Initialize assessment system."""
        self.logger = logging.getLogger(self.__class__.__name__)
        self.required_fields = self._initialize_field_validators()
    def _initialize_field_validators(self) -> Dict[str, FieldValidator]:
        """Initialize field validators with weights and validation functions."""
        return {
            'awb_number': FieldValidator(
                'awb_number', 0.2, self._validate_awb_number
            ),
            'origin_airport': FieldValidator(
                'origin_airport', 0.15, self._validate_airport_code
            ),
            'destination_airport': FieldValidator(
                'destination_airport', 0.15, self._validate_airport_code
            ),
            'shipper_name': FieldValidator(
                'shipper_name', 0.1, self._validate_party_name
            ),
            'consignee_name': FieldValidator(
                'consignee_name', 0.1, self._validate_party_name
            ),
            'total_pieces': FieldValidator(
                'total_pieces', 0.1, self._validate_positive_integer
            ),
            'total_weight': FieldValidator(
                'total_weight', 0.1, self._validate_positive_number
            ),
            'goods_description': FieldValidator(
                'goods_description', 0.1, self._validate_description
            )
        }

    def assess_waybill_quality(self, waybill_data: Dict[str, Any]) -> DataQualityMetrics:
        """
        Assess overall quality of waybill data.

        Args:
            waybill_data: Waybill data dictionary

        Returns:
            DataQualityMetrics: Quality assessment results
        """
        metrics = DataQualityMetrics()

        total_weight = 0.0
        weighted_score = 0.0

        # Assess each field
        for field_name, field_validator in self.required_fields.items():
            field_weight = field_validator.weight
            total_weight += field_weight

            if field_name not in waybill_data or waybill_data[field_name] is None:
                # Missing field
                metrics.missing_fields.append(field_name)
                metrics.field_scores[field_name] = 0.0
                metrics.recommendations.append(f"Add missing field: {field_name}")
            else:
                # Validate field
                field_value = waybill_data[field_name]
                is_valid, validation_score = field_validator.validator_func(field_value)

                if not is_valid:
                    metrics.invalid_fields.append({
                        'field': field_name,
                        'value': field_value,
                        'score': validation_score
                    })

                field_score = validation_score
                metrics.field_scores[field_name] = field_score
                weighted_score += field_score * field_weight

        # Calculate overall scores
        metrics.overall_score = weighted_score / total_weight if total_weight > 0 else 0.0
        metrics.completeness_score = self._calculate_completeness(waybill_data)
        metrics.accuracy_score = self._calculate_accuracy(waybill_data)
        metrics.consistency_score = self._calculate_consistency(waybill_data)

        # Generate recommendations
        self._generate_recommendations(metrics, waybill_data)

        return metrics

    def _validate_awb_number(self, awb_number: Any) -> Tuple[bool, float]:
        """Validate AWB number format."""
        if not awb_number:
            return False, 0.0

        # Convert to string and clean
        awb_str = str(awb_number).replace('-', '').replace(' ', '')

        # Check length (should be 11 digits)
        if len(awb_str) != 11:
            return False, 0.3

        # Check if all characters are digits
        if not awb_str.isdigit():
            return False, 0.5

        # Validate check digit (simplified)
        if self._validate_awb_check_digit(awb_str):
            return True, 1.0
        else:
            return False, 0.8

    def _validate_awb_check_digit(self, awb_number: str) -> bool:
        """Validate AWB check digit using simplified algorithm."""
        try:
            check_digit = int(awb_number[-1])
            number_part = awb_number[:-1]
            calculated_check = sum(int(d) for d in number_part) % 7
            return check_digit == calculated_check
        except (ValueError, IndexError):
            return False

    def _validate_airport_code(self, airport_code: Any) -> Tuple[bool, float]:
        """Validate airport code format."""
        if not airport_code:
            return False, 0.0

        code_str = str(airport_code).strip()

        # Should be 3 characters, all uppercase letters
        if len(code_str) == 3 and code_str.isalpha() and code_str.isupper():
            return True, 1.0
        elif len(code_str) == 3 and code_str.isalpha():
            return True, 0.8  # Valid but not uppercase
        else:
            return False, 0.2

    def _validate_party_name(self, party_name: Any) -> Tuple[bool, float]:
        """Validate party name."""
        if not party_name:
            return False, 0.0

        name_str = str(party_name).strip()

        if len(name_str) < 3:
            return False, 0.3
        elif len(name_str) < 10:
            return True, 0.7
        else:
            return True, 1.0

    def _validate_positive_integer(self, value: Any) -> Tuple[bool, float]:
        """Validate positive integer."""
        try:
            int_value = int(value)
            if int_value > 0:
                return True, 1.0
            else:
                return False, 0.0
        except (ValueError, TypeError):
            return False, 0.0

    def _validate_positive_number(self, value: Any) -> Tuple[bool, float]:
        """Validate positive number."""
        try:
            float_value = float(value)
            if float_value > 0:
                return True, 1.0
            else:
                return False, 0.0
        except (ValueError, TypeError):
            return False, 0.0

    def _validate_description(self, description: Any) -> Tuple[bool, float]:
        """Validate goods description."""
        if not description:
            return False, 0.0

        desc_str = str(description).strip()

        if len(desc_str) < 5:
            return False, 0.3
        elif len(desc_str) < 20:
            return True, 0.7
        else:
            return True, 1.0

    def _calculate_completeness(self, waybill_data: Dict[str, Any]) -> float:
        """Calculate data completeness score."""
        total_fields = len(self.required_fields)
        present_fields = 0

        for field_name in self.required_fields.keys():
            if field_name in waybill_data and waybill_data[field_name] is not None:
                value = waybill_data[field_name]
                if isinstance(value, str) and value.strip():
                    present_fields += 1
                elif not isinstance(value, str) and value:
                    present_fields += 1

        return present_fields / total_fields if total_fields > 0 else 0.0

    def _calculate_accuracy(self, waybill_data: Dict[str, Any]) -> float:
        """Calculate data accuracy score."""
        total_score = 0.0
        field_count = 0

        for field_name, field_validator in self.required_fields.items():
            if field_name in waybill_data and waybill_data[field_name] is not None:
                _, score = field_validator.validator_func(waybill_data[field_name])
                total_score += score
                field_count += 1

        return total_score / field_count if field_count > 0 else 0.0

    def _calculate_consistency(self, waybill_data: Dict[str, Any]) -> float:
        """Calculate data consistency score."""
        consistency_score = 1.0

        # Check for logical inconsistencies
        origin = waybill_data.get('origin_airport')
        destination = waybill_data.get('destination_airport')
        if origin and destination and origin == destination:
            consistency_score -= 0.2

        # Check weight vs pieces ratio
        weight = waybill_data.get('total_weight')
        pieces = waybill_data.get('total_pieces')
        if weight and pieces:
            try:
                weight_per_piece = float(weight) / int(pieces)
                if weight_per_piece < 0.1 or weight_per_piece > 1000:  # Unrealistic ratios
                    consistency_score -= 0.3
            except (ValueError, ZeroDivisionError):
                consistency_score -= 0.1

        # Check shipper vs consignee
        shipper = waybill_data.get('shipper_name')
        consignee = waybill_data.get('consignee_name')
        if shipper and consignee and shipper.strip() == consignee.strip():
            consistency_score -= 0.1

        return max(0.0, consistency_score)

    def _generate_recommendations(self, metrics: DataQualityMetrics,
                                waybill_data: Dict[str, Any]) -> None:
        """Generate improvement recommendations."""
        # Recommendations for missing fields
        for field in metrics.missing_fields:
            metrics.recommendations.append(f"Add missing required field: {field}")

        # Recommendations for invalid fields
        for invalid_field in metrics.invalid_fields:
            field_name = invalid_field['field']
            score = invalid_field['score']
            if score < 0.5:
                metrics.recommendations.append(
                    f"Fix invalid data in field: {field_name}"
                )
            elif score < 0.8:
                metrics.recommendations.append(
                    f"Improve data quality in field: {field_name}"
                )

        # Overall quality recommendations
        if metrics.overall_score < 0.5:
            metrics.recommendations.append("Critical: Overall data quality is poor - review all fields")
        elif metrics.overall_score < 0.7:
            metrics.recommendations.append("Warning: Data quality needs improvement")
        elif metrics.overall_score < 0.9:
            metrics.recommendations.append("Good: Minor improvements possible")
        else:
            metrics.recommendations.append("Excellent: High quality data")
