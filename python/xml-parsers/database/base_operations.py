#!/usr/bin/env python3
"""
Base database operations class for XML parsers.

This module provides a base class for database operations
with common CRUD functionality and error handling.
"""

import json
import logging
from abc import ABC, abstractmethod
from datetime import datetime


class BaseDatabaseOperations(ABC):
    """
    Base class for database operations.

    This class provides common functionality for database operations
    including CRUD operations, transaction management, and error handling.
    """

    def __init__(self, db_connection, db_cursor, logger=None):
        """
        Initialize the base database operations.

        Args:
            db_connection: Database connection object.
            db_cursor: Database cursor object.
            logger (logging.Logger): Logger instance. If None, creates a new one.
        """
        self.db_connection = db_connection
        self.db_cursor = db_cursor
        self.logger = logger or logging.getLogger(self.__class__.__name__)

    def execute_query(self, query, params=None, fetch_one=False, fetch_all=False):
        """
        Execute a database query with error handling.

        Args:
            query (str): SQL query to execute.
            params (tuple): Query parameters.
            fetch_one (bool): Whether to fetch one result.
            fetch_all (bool): Whether to fetch all results.

        Returns:
            Query result or None.
        """
        try:
            self.db_cursor.execute(query, params)

            if fetch_one:
                return self.db_cursor.fetchone()
            elif fetch_all:
                return self.db_cursor.fetchall()

            return None
        except Exception as e:
            self.logger.error(f"Database query error: {e}")
            self.logger.error(f"Query: {query}")
            self.logger.error(f"Params: {params}")
            raise

    def commit_transaction(self):
        """Commit the current database transaction."""
        try:
            self.db_connection.commit()
        except Exception as e:
            self.logger.error(f"Error committing transaction: {e}")
            raise

    def rollback_transaction(self):
        """Rollback the current database transaction."""
        try:
            self.db_connection.rollback()
        except Exception as e:
            self.logger.error(f"Error rolling back transaction: {e}")
            raise

    def insert_record(self, table, data, returning_field=None):
        """
        Insert a record into a table.

        Args:
            table (str): Table name.
            data (dict): Data to insert.
            returning_field (str): Field to return after insert.

        Returns:
            Inserted record ID or None.
        """
        try:
            fields = list(data.keys())
            placeholders = ["%s"] * len(fields)
            values = list(data.values())

            query = f"INSERT INTO {table} ({', '.join(fields)}) VALUES ({', '.join(placeholders)})"

            if returning_field:
                query += f" RETURNING {returning_field}"

            result = self.execute_query(query, values, fetch_one=bool(returning_field))

            if returning_field and result:
                return result[0]

            return None
        except Exception as e:
            self.logger.error(f"Error inserting record into {table}: {e}")
            raise

    def update_record(self, table, data, where_clause, where_params):
        """
        Update a record in a table.

        Args:
            table (str): Table name.
            data (dict): Data to update.
            where_clause (str): WHERE clause.
            where_params (tuple): WHERE clause parameters.

        Returns:
            Number of affected rows.
        """
        try:
            set_clauses = [f"{field} = %s" for field in data.keys()]
            values = list(data.values()) + list(where_params)

            query = f"UPDATE {table} SET {', '.join(set_clauses)} WHERE {where_clause}"

            self.execute_query(query, values)
            return self.db_cursor.rowcount
        except Exception as e:
            self.logger.error(f"Error updating record in {table}: {e}")
            raise

    def delete_record(self, table, where_clause, where_params):
        """
        Delete a record from a table.

        Args:
            table (str): Table name.
            where_clause (str): WHERE clause.
            where_params (tuple): WHERE clause parameters.

        Returns:
            Number of affected rows.
        """
        try:
            query = f"DELETE FROM {table} WHERE {where_clause}"

            self.execute_query(query, where_params)
            return self.db_cursor.rowcount
        except Exception as e:
            self.logger.error(f"Error deleting record from {table}: {e}")
            raise

    def find_record(self, table, where_clause, where_params, fields="*"):
        """
        Find a single record in a table.

        Args:
            table (str): Table name.
            where_clause (str): WHERE clause.
            where_params (tuple): WHERE clause parameters.
            fields (str): Fields to select.

        Returns:
            Record tuple or None.
        """
        try:
            query = f"SELECT {fields} FROM {table} WHERE {where_clause}"
            return self.execute_query(query, where_params, fetch_one=True)
        except Exception as e:
            self.logger.error(f"Error finding record in {table}: {e}")
            raise

    def find_records(
        self,
        table,
        where_clause=None,
        where_params=None,
        fields="*",
        order_by=None,
        limit=None,
    ):
        """
        Find multiple records in a table.

        Args:
            table (str): Table name.
            where_clause (str): WHERE clause.
            where_params (tuple): WHERE clause parameters.
            fields (str): Fields to select.
            order_by (str): ORDER BY clause.
            limit (int): LIMIT clause.

        Returns:
            List of record tuples.
        """
        try:
            query = f"SELECT {fields} FROM {table}"

            if where_clause:
                query += f" WHERE {where_clause}"

            if order_by:
                query += f" ORDER BY {order_by}"

            if limit:
                query += f" LIMIT {limit}"

            return self.execute_query(query, where_params, fetch_all=True)
        except Exception as e:
            self.logger.error(f"Error finding records in {table}: {e}")
            raise

    def record_exists(self, table, where_clause, where_params):
        """
        Check if a record exists in a table.

        Args:
            table (str): Table name.
            where_clause (str): WHERE clause.
            where_params (tuple): WHERE clause parameters.

        Returns:
            bool: True if record exists, False otherwise.
        """
        try:
            query = f"SELECT 1 FROM {table} WHERE {where_clause} LIMIT 1"
            result = self.execute_query(query, where_params, fetch_one=True)
            return result is not None
        except Exception as e:
            self.logger.error(f"Error checking record existence in {table}: {e}")
            raise

    def get_or_create_record(
        self, table, search_data, create_data=None, returning_field="id"
    ):
        """
        Get an existing record or create a new one.

        Args:
            table (str): Table name.
            search_data (dict): Data to search for existing record.
            create_data (dict): Data to create new record (defaults to search_data).
            returning_field (str): Field to return.

        Returns:
            Record ID and boolean indicating if it was created.
        """
        try:
            # Build WHERE clause from search_data
            where_clauses = [f"{field} = %s" for field in search_data.keys()]
            where_clause = " AND ".join(where_clauses)
            where_params = tuple(search_data.values())

            # Try to find existing record
            result = self.find_record(
                table, where_clause, where_params, returning_field
            )

            if result:
                return result[0], False  # Found existing record

            # Create new record
            data_to_insert = create_data or search_data
            record_id = self.insert_record(table, data_to_insert, returning_field)
            return record_id, True  # Created new record

        except Exception as e:
            self.logger.error(f"Error in get_or_create for {table}: {e}")
            raise

    def format_json_field(self, data):
        """
        Format data for JSON/JSONB fields.

        Args:
            data: Data to format.

        Returns:
            str: JSON string.
        """
        if data is None:
            return None

        if isinstance(data, (dict, list)):
            return json.dumps(data)

        return str(data)

    def add_timestamps(self, data, created_by=None, updated_by=None):
        """
        Add timestamp fields to data.

        Args:
            data (dict): Data dictionary to modify.
            created_by (int): User ID for created_by field.
            updated_by (int): User ID for updated_by field.

        Returns:
            dict: Modified data dictionary.
        """
        now = datetime.now()

        if "created_at" not in data:
            data["created_at"] = now

        if "updated_at" not in data:
            data["updated_at"] = now

        if created_by is not None and "created_by" not in data:
            data["created_by"] = created_by

        if updated_by is not None and "updated_by" not in data:
            data["updated_by"] = updated_by

        return data

    @abstractmethod
    def save_data(self, data):
        """
        Save parsed data to the database. Must be implemented by subclasses.

        Args:
            data (dict): Parsed data to save.

        Returns:
            dict: Result of save operation.
        """
        pass
