#!/usr/bin/env python3
"""
Enhanced Integrated database operations for the cargo XML parser system.

This module provides database operations for the enhanced XFWB + XFFM + XFZB
processing system with:
- Processing status lifecycle management (PENDING -> AWAITING_CHECKIN -> CHECKED_IN -> RECONCILED)
- Expected vs declared vs checked-in field separation
- Partial suffix generation (one per flight arrival)
- SHA-256 duplicate prevention for files and AWBs
- In-transit logic based on destination branches
- Comprehensive audit trails and logging
"""

import hashlib
import json
import logging
import os
import uuid
import sys
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any

from .base_operations import BaseDatabaseOperations

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.awb_utils import normalize_awb_number


class IntegratedDatabaseOperations(BaseDatabaseOperations):
    """
    Enhanced database operations for the integrated cargo XML parser system.

    Implements the new business logic requirements:
    - Processing status lifecycle (PENDING -> AWAITING_CHECKIN -> CHECKED_IN -> RECONCILED)
    - Expected/declared/checked-in field separation
    - Partial suffix generation (one per flight arrival)
    - File and AWB content duplicate prevention
    - In-transit logic based on destination branches
    - Comprehensive audit trails and logging
    """

    def __init__(self, db_connection, db_cursor, branch_id=1, user_id=1, logger=None):
        """
        Initialize integrated database operations.

        Args:
            db_connection: Database connection object
            db_cursor: Database cursor object
            branch_id (int): Current branch ID for in-transit logic
            user_id (int): Current user ID for audit trails
            logger: Logger instance
        """
        super().__init__(db_connection, db_cursor, logger)
        self.branch_id = branch_id
        self.user_id = user_id

    # ==================== ENHANCED FILE DUPLICATE PREVENTION ====================

    def generate_file_hash(self, file_path: str) -> str:
        """
        Generate SHA-256 hash for entire file content.

        Args:
            file_path (str): Path to the file

        Returns:
            str: SHA-256 hash string
        """
        sha256_hash = hashlib.sha256()
        try:
            with open(file_path, "rb") as f:
                for byte_block in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(byte_block)
            return sha256_hash.hexdigest()
        except Exception as e:
            self.logger.error(f"Error generating file hash for {file_path}: {str(e)}")
            raise

    def check_file_duplicate(self, file_hash: str) -> Optional[Dict[str, Any]]:
        """
        Check if file with given hash already exists.

        Args:
            file_hash (str): SHA-256 hash of file

        Returns:
            dict: Existing file record if duplicate, None otherwise
        """
        result = self.find_record(
            'duplicate_files',
            'file_hash = %s',
            (file_hash,),
            'id, file_name, first_processed_at, attempt_count'
        )

        if result:
            return {
                'id': result[0],
                'file_name': result[1],
                'first_processed_at': result[2],
                'attempt_count': result[3]
            }
        return None

    def record_file_processed(self, file_path: str, file_hash: str, message_type: str, file_size: int) -> int:
        """
        Record file as processed in duplicate prevention system.

        Args:
            file_path (str): Path to the file
            file_hash (str): SHA-256 hash of file
            message_type (str): XFFM, XFWB, or XFZB
            file_size (int): File size in bytes

        Returns:
            int: Record ID
        """
        data = {
            'file_name': os.path.basename(file_path),
            'file_hash': file_hash,
            'message_type': message_type.upper(),
            'file_path': file_path,
            'file_size': file_size,
            'first_processed_at': datetime.now(),
            'last_attempted_at': datetime.now(),
            'attempt_count': 1,
            'branch_id': self.branch_id,
            'created_by': self.user_id,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }

        return self.insert_record('duplicate_files', data, 'id')

    def record_duplicate_attempt(self, file_hash: str) -> None:
        """
        Record a duplicate file attempt.

        Args:
            file_hash (str): SHA-256 hash of file
        """
        self.execute_query(
            """UPDATE duplicate_files
               SET attempt_count = attempt_count + 1,
                   last_attempted_at = %s
               WHERE file_hash = %s""",
            (datetime.now(), file_hash)
        )

    # ==================== ENHANCED AWB CONTENT DUPLICATE PREVENTION ====================

    def generate_awb_content_hash(self, awb_data: Dict[str, Any]) -> str:
        """
        Generate SHA-256 hash for AWB content (enhanced version).

        Args:
            awb_data (dict): AWB data containing pieces, weight, volume, etc.

        Returns:
            str: SHA-256 hash string
        """
        # Create normalized data for consistent hashing
        normalized_data = {
            'awb_number': awb_data.get('awb_number', ''),
            'origin': awb_data.get('origin_airport', ''),
            'destination': awb_data.get('destination_airport', ''),
            'pieces': awb_data.get('pieces', awb_data.get('total_pieces', 0)),
            'weight': float(awb_data.get('weight', awb_data.get('total_weight', 0))),
            'volume': float(awb_data.get('volume', awb_data.get('gross_volume', 0))),
            'description': awb_data.get('description', awb_data.get('summary_description', '')),
            'special_handling': awb_data.get('special_handling', awb_data.get('special_handling_code', '')),
        }

        # Sort keys for consistency
        normalized_json = json.dumps(normalized_data, sort_keys=True)
        return hashlib.sha256(normalized_json.encode('utf-8')).hexdigest()

    def check_awb_content_duplicate(self, awb_number: str, content_hash: str) -> bool:
        """
        Check if AWB with identical content already exists.

        Args:
            awb_number (str): AWB number
            content_hash (str): SHA-256 hash of AWB content

        Returns:
            bool: True if duplicate exists, False otherwise
        """
        return self.record_exists(
            'awb_hashes',
            'awb_number = %s AND content_hash = %s',
            (awb_number, content_hash)
        )

    def record_awb_processed(self, awb_number: str, content_hash: str, manifest_id: str, source_file: str) -> int:
        """
        Record AWB as processed in duplicate prevention system.

        Args:
            awb_number (str): AWB number
            content_hash (str): SHA-256 hash of AWB content
            manifest_id (str): Manifest ID
            source_file (str): Source file name

        Returns:
            int: Record ID
        """
        data = {
            'awb_number': awb_number,
            'manifest_id': manifest_id,
            'content_hash': content_hash,
            'source_file': source_file,
            'processed_at': datetime.now(),
            'branch_id': self.branch_id,
            'created_by': self.user_id,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }

        return self.insert_record('awb_hashes', data, 'id')

    # ==================== ENHANCED PROCESSING LOG ====================

    def start_processing_log(self, file_name: str, message_type: str) -> str:
        """
        Start a processing log session.

        Args:
            file_name (str): Name of file being processed
            message_type (str): XFFM, XFWB, or XFZB

        Returns:
            str: Unique log ID
        """
        log_id = f"LOG-{uuid.uuid4()}"

        data = {
            'log_id': log_id,
            'file_name': file_name,
            'message_type': message_type.upper(),
            'status': 'STARTED',
            'started_at': datetime.now(),
            'awbs_processed': 0,
            'ulds_processed': 0,
            'errors_count': 0,
            'branch_id': self.branch_id,
            'created_by': self.user_id,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }

        self.insert_record('processing_logs', data)
        return log_id

    def complete_processing_log(self, log_id: str, success: bool, awbs_processed: int = 0,
                               ulds_processed: int = 0, error_message: str = None,
                               processing_summary: Dict[str, Any] = None) -> None:
        """
        Complete a processing log session.

        Args:
            log_id (str): Log ID from start_processing_log
            success (bool): Whether processing was successful
            awbs_processed (int): Number of AWBs processed
            ulds_processed (int): Number of ULDs processed
            error_message (str): Error message if failed
            processing_summary (dict): Summary of processing results
        """
        # Calculate duration
        result = self.find_record(
            'processing_logs',
            'log_id = %s',
            (log_id,),
            'started_at'
        )

        duration_seconds = 0
        if result:
            started_at = result[0]
            duration_seconds = int((datetime.now() - started_at).total_seconds())

        update_data = {
            'status': 'SUCCESS' if success else 'FAILED',
            'completed_at': datetime.now(),
            'duration_seconds': duration_seconds,
            'awbs_processed': awbs_processed,
            'ulds_processed': ulds_processed,
            'error_message': error_message,
            'processing_summary': self.format_json_field(processing_summary),
            'updated_at': datetime.now()
        }

        self.update_record(
            'processing_logs',
            update_data,
            'log_id = %s',
            (log_id,)
        )

    def mark_processing_duplicate(self, log_id: str) -> None:
        """
        Mark processing log as duplicate.

        Args:
            log_id (str): Log ID
        """
        update_data = {
            'status': 'DUPLICATE',
            'completed_at': datetime.now(),
            'updated_at': datetime.now()
        }

        self.update_record(
            'processing_logs',
            update_data,
            'log_id = %s',
            (log_id,)
        )

    # ==================== PARTIAL SUFFIX TRACKING ====================

    def generate_partial_suffix(self, awb_number: str, manifest_id: str, flight_number: str,
                                flight_date: str, split_type: str) -> str:
        """
        Generate suffix for partial shipments (P and D types).
        One suffix per flight arrival regardless of ULD count.

        Args:
            awb_number (str): AWB number
            manifest_id (str): Manifest ID
            flight_number (str): Flight number
            flight_date (str): Flight date
            split_type (str): P or D

        Returns:
            str: Generated suffix (e.g., "AWB-1", "AWB-2")
        """
        # Check if enhanced suffix tracking is available
        if not self.enhanced_features.get('partial_suffix_tracking', False):
            # Fallback: Use simple sequence-based suffix
            try:
                result = self.execute_query(
                    "SELECT COALESCE(MAX(CAST(SUBSTRING(partial_id FROM '[0-9]+$') AS INTEGER)), 0) + 1 FROM partial_waybills WHERE master_awb_number = %s",
                    (awb_number,),
                    fetch_one=True
                )
                next_suffix = result[0] if result else 1
                return f"{awb_number}-{next_suffix}"
            except Exception:
                # Ultimate fallback: timestamp-based suffix
                import time
                return f"{awb_number}-{int(time.time() % 10000)}"

        # Enhanced suffix tracking (requires partial_suffix_tracking table)
        try:
            # Check if suffix already exists for this flight
            existing = self.find_record(
                'partial_suffix_tracking',
                'awb_number = %s AND manifest_id = %s',
                (awb_number, manifest_id),
                'generated_suffix'
            )

            if existing:
                return existing[0]

            # Get next suffix number for this AWB
            result = self.execute_query(
                "SELECT COALESCE(MAX(suffix_number), 0) + 1 FROM partial_suffix_tracking WHERE awb_number = %s",
                (awb_number,),
                fetch_one=True
            )

            next_suffix = result[0] if result else 1
            generated_suffix = f"{awb_number}-{next_suffix}"

            # Create tracking record
            data = {
                'awb_number': awb_number,
                'flight_number': flight_number,
                'flight_date': flight_date,
                'manifest_id': manifest_id,
                'suffix_number': next_suffix,
                'generated_suffix': generated_suffix,
                'split_type': split_type,
                'branch_id': self.branch_id,
                'created_at': datetime.now()
            }

            self.insert_record('partial_suffix_tracking', data)
            return generated_suffix

        except Exception as e:
            self.logger.warning(f"Enhanced suffix tracking failed, using fallback: {e}")
            # Fallback to simple suffix generation
            try:
                result = self.execute_query(
                    "SELECT COALESCE(MAX(CAST(SUBSTRING(partial_id FROM '[0-9]+$') AS INTEGER)), 0) + 1 FROM partial_waybills WHERE master_awb_number = %s",
                    (awb_number,),
                    fetch_one=True
                )
                next_suffix = result[0] if result else 1
                return f"{awb_number}-{next_suffix}"
            except Exception:
                # Ultimate fallback
                import time
                return f"{awb_number}-{int(time.time() % 10000)}"

    def generate_awb_hash(self, awb_data: Dict[str, Any]) -> str:
        """
        Generate SHA-256 hash for AWB cargo information.

        Args:
            awb_data (dict): AWB data containing pieces, weight, volume

        Returns:
            str: SHA-256 hash string
        """
        # Create a consistent string representation of cargo info
        cargo_info = {
            'pieces': awb_data.get('total_pieces', 0),
            'weight': float(awb_data.get('total_weight', 0)),
            'volume': float(awb_data.get('gross_volume', 0)),
            'origin': awb_data.get('origin_airport', ''),
            'destination': awb_data.get('destination_airport', '')
        }

        # Sort keys for consistent hashing
        cargo_string = json.dumps(cargo_info, sort_keys=True)
        return hashlib.sha256(cargo_string.encode('utf-8')).hexdigest()

    def check_awb_hash_exists(self, awb_number: str, hash_value: str) -> bool:
        """
        Check if AWB hash already exists (duplicate prevention).

        Args:
            awb_number (str): AWB number
            hash_value (str): SHA-256 hash

        Returns:
            bool: True if hash exists, False otherwise
        """
        return self.record_exists(
            'awb_hashes',
            'awb_number = %s AND hash = %s',
            (awb_number, hash_value)
        )

    def store_awb_hash(self, awb_number: str, hash_value: str) -> int:
        """
        Store AWB hash for duplicate prevention.

        Args:
            awb_number (str): AWB number
            hash_value (str): SHA-256 hash

        Returns:
            int: Hash record ID
        """
        data = {
            'awb_number': awb_number,
            'hash': hash_value,
            'created_at': datetime.now()
        }

        return self.insert_record('awb_hashes', data, 'id')

    def log_processing(self, file_name: str, message_id: str, msg_type: str,
                      awb_count: int = 0, partial_count: int = 0,
                      processing_time_ms: int = 0, success: bool = True,
                      error_message: str = None) -> int:
        """
        Log XML processing activity.

        Args:
            file_name (str): Name of processed file
            message_id (str): Message ID from XML
            msg_type (str): Message type (XFWB, XFFM, XFZB)
            awb_count (int): Number of AWBs processed
            partial_count (int): Number of partial records created
            processing_time_ms (int): Processing time in milliseconds
            success (bool): Whether processing was successful
            error_message (str): Error message if any

        Returns:
            int: Log record ID
        """
        data = {
            'file_name': file_name,
            'message_id': message_id,
            'type': msg_type,
            'awb_count': awb_count,
            'partial_count': partial_count,
            'parsed_at': datetime.now(),
            'processing_time_ms': processing_time_ms,
            'success': success,
            'error_message': error_message,
            'branch_id': self.branch_id,
            'processed_by': self.user_id
        }

        return self.insert_record('processing_log', data, 'id')

    def get_branch_code(self, branch_id: int) -> Optional[str]:
        """
        Get branch code for in-transit logic.

        Args:
            branch_id (int): Branch ID

        Returns:
            str: Branch code or None
        """
        result = self.find_record(
            'branches',
            'id = %s',
            (branch_id,),
            'code'
        )
        return result[0] if result else None

    def check_destination_branch_exists(self, destination_code: str) -> Optional[int]:
        """
        Check if destination airport has a corresponding branch.

        Args:
            destination_code (str): Destination airport code

        Returns:
            int: Branch ID if exists, None otherwise
        """
        result = self.find_record(
            'branches',
            'code = %s',
            (destination_code,),
            'id'
        )
        return result[0] if result else None

    def create_or_update_master_waybill_enhanced(self, awb_data: Dict[str, Any],
                                                 source_type: str = 'XFFM') -> Tuple[str, bool]:
        """
        Create or update master waybill with enhanced processing logic.

        Args:
            awb_data (dict): AWB data
            source_type (str): XFFM or XFWB

        Returns:
            tuple: (awb_number, was_created)
        """
        awb_number = awb_data['awb_number']

        # Check if AWB already exists
        existing = self.find_record(
            'master_waybills',
            'awb_number = %s',
            (awb_number,),
            'awb_number, processing_status, total_pieces_expected, total_pieces_declared'
        )

        if existing:
            return self._update_existing_master_waybill(awb_number, awb_data, source_type, existing)
        else:
            return self._create_new_master_waybill(awb_data, source_type)

    def _update_existing_master_waybill(self, awb_number: str, awb_data: Dict[str, Any],
                                       source_type: str, existing: Tuple) -> Tuple[str, bool]:
        """Update existing master waybill."""
        update_data = {
            'updated_at': datetime.now(),
            'updated_by': self.user_id
        }

        if source_type == 'XFFM':
            # Update expected fields from manifest
            pieces = awb_data.get('pieces', 0)
            weight = awb_data.get('weight', 0)
            volume = awb_data.get('volume', 0)

            update_data.update({
                'total_pieces_expected': (existing[2] or 0) + pieces,
                'gross_weight_expected': awb_data.get('weight'),
                'volume_expected': awb_data.get('volume'),
            })

            # Update processing status if needed
            if existing[1] == 'PENDING':
                update_data['processing_status'] = 'AWAITING_CHECKIN'

        elif source_type == 'XFWB':
            # Update declared fields from XFWB
            update_data.update({
                'total_pieces_declared': awb_data.get('total_pieces'),
                'gross_weight_declared': awb_data.get('total_weight'),
                'volume_declared': awb_data.get('gross_volume'),
                'shipper_code': awb_data.get('shipper_code'),
                'consignee_code': awb_data.get('consignee_code'),
                'special_handling_codes': self.format_json_field(awb_data.get('special_handling_codes')),
                'goods_descriptions': self.format_json_field(awb_data.get('goods_descriptions')),
                'xml_data': self.format_json_field(awb_data.get('xml_data'))
            })

            # Update processing status
            if existing[1] == 'PENDING':
                update_data['processing_status'] = 'AWAITING_CHECKIN'

        # Handle in-transit logic
        self._update_in_transit_status(update_data, awb_data)

        self.update_record(
            'master_waybills',
            update_data,
            'awb_number = %s',
            (awb_number,)
        )

        return awb_number, False

    def _create_new_master_waybill(self, awb_data: Dict[str, Any], source_type: str) -> Tuple[str, bool]:
        """Create new master waybill."""
        awb_number = awb_data['awb_number']

        data = {
            'awb_number': awb_number,
            'type_code': awb_data.get('type_code', '740'),
            'manifest_id': awb_data.get('manifest_id'),
            'origin_airport': awb_data.get('origin_airport'),
            'destination_airport': awb_data.get('destination_airport'),
            'processing_status': 'PENDING',
            'branch_id': self.branch_id,
            'created_by': self.user_id,
            'updated_by': self.user_id,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }

        if source_type == 'XFFM':
            # Set expected fields from manifest
            data.update({
                'total_pieces_expected': awb_data.get('pieces', 0),
                'gross_weight_expected': awb_data.get('weight'),
                'volume_expected': awb_data.get('volume'),
                'processing_status': 'AWAITING_CHECKIN'
            })
        elif source_type == 'XFWB':
            # Set declared fields from XFWB
            data.update({
                'total_pieces': awb_data.get('total_pieces', 0),
                'total_weight': awb_data.get('total_weight', 0),
                'total_pieces_declared': awb_data.get('total_pieces'),
                'gross_weight_declared': awb_data.get('total_weight'),
                'volume_declared': awb_data.get('gross_volume'),
                'weight_unit': awb_data.get('weight_unit', 'KGM'),
                'shipper_code': awb_data.get('shipper_code'),
                'consignee_code': awb_data.get('consignee_code'),
                'special_handling_codes': self.format_json_field(awb_data.get('special_handling_codes')),
                'goods_descriptions': self.format_json_field(awb_data.get('goods_descriptions')),
                'xml_data': self.format_json_field(awb_data.get('xml_data')),
                'processing_status': 'AWAITING_CHECKIN'
            })

        # Handle in-transit logic
        self._update_in_transit_status(data, awb_data)

        self.insert_record('master_waybills', data)
        return awb_number, True

    def _update_in_transit_status(self, data: Dict[str, Any], awb_data: Dict[str, Any]) -> None:
        """Update in-transit status based on destination."""
        destination = awb_data.get('destination_airport')
        if destination:
            # Check if destination has a corresponding branch
            destination_branch = self.check_destination_branch_exists(destination)
            if destination_branch and destination_branch != self.branch_id:
                data['in_transit'] = True
            else:
                data['in_transit'] = False

    def create_or_update_master_waybill(self, awb_data: Dict[str, Any],
                                       is_orphan: bool = False) -> Tuple[str, bool]:
        """
        Create or update master waybill record.

        Args:
            awb_data (dict): AWB data
            is_orphan (bool): Whether this is an orphan AWB from XFFM

        Returns:
            tuple: (awb_number, was_created)
        """
        awb_number = normalize_awb_number(awb_data['awb_number'])

        # Check if AWB already exists
        existing = self.find_record(
            'master_waybills',
            'awb_number = %s',
            (awb_number,),
            'awb_id, status'
        )

        if existing:
            # Update existing record
            update_data = {
                'updated_at': datetime.now(),
                'updated_by': self.user_id
            }

            # If it was an orphan and now we have proper data, update status
            if existing[1] == 'UNVERIFIED_ORPHAN' and not is_orphan:
                update_data['status'] = 'EXPECTED'
                update_data['total_pieces_expected'] = awb_data.get('total_pieces', 0)
                update_data['gross_volume'] = awb_data.get('gross_volume')
                update_data['volume_unit'] = awb_data.get('volume_unit')
                update_data['summary_description'] = awb_data.get('summary_description')

            self.update_record(
                'master_waybills',
                update_data,
                'awb_number = %s',
                (awb_number,)
            )
            return awb_number, False
        else:
            # Create new record
            data = awb_data.copy()
            data['awb_number'] = awb_number

            # Add default values
            data.setdefault('type_code', '740')
            data.setdefault('processing_status', 'PENDING')
            data.setdefault('branch_id', self.branch_id)
            data.setdefault('created_by', self.user_id)
            data.setdefault('updated_by', self.user_id)
            data.setdefault('created_at', datetime.now())
            data.setdefault('updated_at', datetime.now())

            # Set expected fields from manifest
            data['total_pieces_expected'] = awb_data.get('pieces', 0)
            data['gross_weight_expected'] = awb_data.get('weight')
            data['volume_expected'] = awb_data.get('volume')

            # Set declared fields from XFWB if available
            if 'total_pieces' in awb_data:
                data['total_pieces_declared'] = awb_data.get('total_pieces')
                data['gross_weight_declared'] = awb_data.get('total_weight')
                data['volume_declared'] = awb_data.get('gross_volume')
                data['shipper_code'] = awb_data.get('shipper_code')
                data['consignee_code'] = awb_data.get('consignee_code')
                data['special_handling_codes'] = self.format_json_field(awb_data.get('special_handling_codes'))
                data['goods_descriptions'] = self.format_json_field(awb_data.get('goods_descriptions'))
                data['xml_data'] = self.format_json_field(awb_data.get('xml_data'))

            # Determine processing status
            if awb_data.get('pieces', 0) > 0:
                data['processing_status'] = 'AWAITING_CHECKIN'
            else:
                data['processing_status'] = 'PENDING'

            self.insert_record('master_waybills', data)
            return awb_number, True

    def create_partial_waybill_enhanced(self, awb_number: str, partial_data: Dict[str, Any],
                                       suffix: str, uld_id: str = None, manifest_id: str = None) -> str:
        """
        Create partial waybill record with enhanced processing logic.

        Args:
            awb_number (str): Parent AWB number
            partial_data (dict): Partial shipment data
            suffix (str): Partial suffix (e.g., 'AWB-1')
            uld_id (str): ULD ID if applicable
            manifest_id (str): Manifest ID

        Returns:
            str: Partial ID
        """
        partial_id = suffix  # Use the generated suffix as partial ID

        data = {
            'partial_id': partial_id,
            'master_awb_number': awb_number,
            'manifest_id': manifest_id or partial_data.get('manifest_id'),
            'flight_number': partial_data.get('flight_number'),
            'flight_date': partial_data.get('flight_date'),
            'origin_airport': partial_data.get('origin_airport'),
            'destination_airport': partial_data.get('destination_airport'),
            'manifest_expected_pieces': partial_data.get('pieces', 0),
            'manifest_expected_weight': partial_data.get('weight', 0),
            'manifest_expected_volume': partial_data.get('volume', 0),
            'expected_pieces': partial_data.get('pieces', 0),
            'expected_weight': partial_data.get('weight', 0),
            'received_pieces': 0,  # Will be updated by check-in module
            'received_weight': 0,  # Will be updated by check-in module
            'remaining_pieces': partial_data.get('pieces', 0),
            'remaining_weight': partial_data.get('weight', 0),
            'transport_split_description': partial_data.get('split_type', 'P'),
            'partial_suffix': suffix,
            'processing_status': 'AWAITING_CHECKIN',
            'status': 'PENDING',
            'uld_id': uld_id,
            'weight_unit': partial_data.get('weight_unit', 'KGM'),
            'special_handling_code': partial_data.get('special_handling_code'),
            'source': 'XFFM',
            'content_hash': self.generate_awb_content_hash(partial_data),
            'branch_id': self.branch_id,
            'created_by': self.user_id,
            'updated_by': self.user_id,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }

        self.insert_record('partial_waybills', data)
        return partial_id

    def create_partial_waybill(self, awb_number: str, partial_data: Dict[str, Any],
                              suffix: str, uld_id: str = None) -> str:
        """
        Create partial waybill record for split shipments.

        Args:
            awb_number (str): Parent AWB number
            partial_data (dict): Partial shipment data
            suffix (str): Partial suffix (e.g., '-1', '-2')
            uld_id (str): ULD ID if applicable

        Returns:
            str: Partial ID
        """
        partial_id = f"{awb_number}{suffix}"

        data = {
            'partial_id': partial_id,
            'master_awb_number': awb_number,
            'manifest_id': partial_data.get('manifest_id'),
            'origin_airport': partial_data['origin_airport'],
            'destination_airport': partial_data['destination_airport'],
            'expected_pieces': partial_data.get('pieces', 0),
            'expected_weight': partial_data.get('weight', 0),
            'received_pieces': partial_data.get('pieces', 0),
            'received_weight': partial_data.get('weight', 0),
            'remaining_pieces': 0,
            'remaining_weight': 0,
            'split_code': partial_data.get('split_code', 'P'),
            'split_sequence': partial_data.get('split_sequence', 1),
            'status': 'CHECKED_IN',
            'special_handling_code': partial_data.get('special_handling_code'),
            'uld_id': uld_id,
            'weight_unit': partial_data.get('weight_unit', 'KGM'),
            'gross_volume': partial_data.get('volume'),
            'volume_unit': partial_data.get('volume_unit'),
            'summary_description': partial_data.get('summary_description'),
            'source': 'XFFM',
            'branch_id': self.branch_id,
        }

        # Add timestamps and user info
        self.add_timestamps(data, self.user_id, self.user_id)

        self.insert_record('partial_waybills', data)
        return partial_id

    def create_uld_record(self, uld_data: Dict[str, Any]) -> int:
        """
        Create ULD detail record.

        Args:
            uld_data (dict): ULD data

        Returns:
            int: ULD record ID
        """
        # Check if ULD already exists
        existing = self.find_record(
            'uld_details',
            'uld_id = %s AND manifest_id = %s',
            (uld_data['uld_id'], uld_data.get('manifest_id')),
            'id'
        )

        if existing:
            self.logger.info(f"ULD {uld_data['uld_id']} already exists for manifest {uld_data.get('manifest_id')}")
            return existing[0]

        # Basic ULD data that should exist in all schema versions
        data = {
            'manifest_id': uld_data.get('manifest_id'),
            'uld_id': uld_data['uld_id'],
            'uld_type': uld_data.get('uld_type'),
            'uld_owner': uld_data.get('uld_owner'),
            'weight': uld_data.get('weight', 0),
            'pieces': uld_data.get('pieces', 0),
            'branch_id': self.branch_id,
        }

        # Add optional fields if they exist in the schema
        try:
            # Check if enhanced columns exist
            self.db_cursor.execute("""
                SELECT column_name FROM information_schema.columns
                WHERE table_name = 'uld_details' AND column_name IN ('status', 'current_location', 'arrived_at', 'loading_indicator')
            """)
            available_columns = [row[0] for row in self.db_cursor.fetchall()]

            if 'loading_indicator' in available_columns:
                data['loading_indicator'] = uld_data.get('loading_indicator')
            if 'status' in available_columns:
                data['status'] = 'ARRIVED'
            if 'current_location' in available_columns:
                data['current_location'] = uld_data.get('current_location')
            if 'arrived_at' in available_columns:
                data['arrived_at'] = datetime.now()

        except Exception as e:
            self.logger.warning(f"Could not check ULD schema columns: {str(e)}")

        # Add timestamps and user info
        self.add_timestamps(data, self.user_id, self.user_id)

        try:
            uld_id = self.insert_record('uld_details', data, 'id')
            self.logger.info(f"✅ Created ULD record: {uld_data['uld_id']} (ID: {uld_id})")
            return uld_id
        except Exception as e:
            self.logger.error(f"❌ Error creating ULD record for {uld_data['uld_id']}: {str(e)}")
            # Try with minimal data if enhanced insert fails
            minimal_data = {
                'manifest_id': uld_data.get('manifest_id'),
                'uld_id': uld_data['uld_id'],
                'uld_type': uld_data.get('uld_type'),
                'uld_owner': uld_data.get('uld_owner'),
                'weight': uld_data.get('weight', 0),
                'pieces': uld_data.get('pieces', 0),
                'branch_id': self.branch_id,
            }
            self.add_timestamps(minimal_data, self.user_id, self.user_id)

            uld_id = self.insert_record('uld_details', minimal_data, 'id')
            self.logger.info(f"✅ Created ULD record with minimal data: {uld_data['uld_id']} (ID: {uld_id})")
            return uld_id

    def update_master_waybill_pieces(self, awb_number: str, pieces_to_add: int):
        """
        Update received pieces count for master waybill.

        Args:
            awb_number (str): AWB number
            pieces_to_add (int): Number of pieces to add
        """
        # Get current counts
        result = self.find_record(
            'master_waybills',
            'awb_number = %s',
            (awb_number,),
            'received_pieces_count, total_pieces_expected'
        )

        if result:
            current_received = result[0] or 0
            total_expected = result[1] or 0
            new_received = current_received + pieces_to_add

            # Determine new status
            if new_received >= total_expected and total_expected > 0:
                new_status = 'RECEIVED_COMPLETE'
            elif new_received > 0:
                new_status = 'IN_PROGRESS'
            else:
                new_status = 'EXPECTED'

            # Update the record
            update_data = {
                'received_pieces_count': new_received,
                'status': new_status,
                'updated_at': datetime.now(),
                'updated_by': self.user_id
            }

            self.update_record(
                'master_waybills',
                update_data,
                'awb_number = %s',
                (awb_number,)
            )

    def get_partial_sequence_number(self, awb_number: str) -> int:
        """
        Get next sequence number for partial waybill.

        Args:
            awb_number (str): AWB number

        Returns:
            int: Next sequence number
        """
        result = self.execute_query(
            "SELECT COALESCE(MAX(split_sequence), 0) + 1 FROM partial_waybills WHERE master_awb_number = %s",
            (awb_number,),
            fetch_one=True
        )
        return result[0] if result else 1

    # ==================== ENHANCED SPLIT TYPE HANDLING ====================

    def handle_split_types(self, awb_data: Dict[str, Any], uld_data: Dict[str, Any] = None,
                          manifest_id: str = None) -> Dict[str, Any]:
        """
        Handle different split types (T, S, P, D) according to business rules.

        Args:
            awb_data (dict): AWB data
            uld_data (dict): ULD data if applicable
            manifest_id (str): Manifest ID

        Returns:
            dict: Processing result
        """
        split_type = awb_data.get('transport_split_description', 'T')
        awb_number = awb_data['awb_number']

        result = {
            'awb_number': awb_number,
            'split_type': split_type,
            'partial_created': False,
            'suffix': None,
            'uld_recorded': False
        }

        # Create or update master waybill
        master_awb, was_created = self.create_or_update_master_waybill_enhanced(awb_data, 'XFFM')
        result['master_created'] = was_created

        # Handle ULD if present
        if uld_data:
            # Add manifest_id to uld_data
            uld_data_with_manifest = uld_data.copy()
            uld_data_with_manifest['manifest_id'] = manifest_id
            uld_id = self.create_uld_record(uld_data_with_manifest)
            result['uld_recorded'] = True
            result['uld_id'] = uld_id

        # Handle split types
        if split_type in ['P', 'D']:  # Partial shipments require suffix
            suffix = self.generate_partial_suffix(
                awb_number,
                manifest_id,
                awb_data.get('flight_number', 'UNKNOWN'),
                awb_data.get('flight_date', datetime.now().strftime('%Y-%m-%d')),
                split_type
            )

            partial_id = self.create_partial_waybill_enhanced(
                awb_number,
                awb_data,
                suffix,
                uld_data.get('uld_id') if uld_data else None,
                manifest_id
            )

            result['partial_created'] = True
            result['suffix'] = suffix
            result['partial_id'] = partial_id

            # Update master waybill to mark as partial
            self.update_record(
                'master_waybills',
                {'is_partial': True, 'updated_at': datetime.now()},
                'awb_number = %s',
                (awb_number,)
            )

        return result

    def save_data(self, data):
        """
        Save parsed data to the database. Implementation required by base class.

        Args:
            data (dict): Parsed data to save

        Returns:
            dict: Result of save operation
        """
        # This will be implemented by specific processor classes
        raise NotImplementedError("save_data must be implemented by specific processor classes")
