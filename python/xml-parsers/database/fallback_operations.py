#!/usr/bin/env python3
"""
Fallback operations for enhanced functionality when new tables don't exist.

This module provides fallback implementations that work with the existing
database schema while providing enhanced functionality where possible.
"""

import hashlib
import json
import logging
import os
import uuid
import sys
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any

from .integrated_operations import IntegratedDatabaseOperations
from utils.awb_utils import normalize_awb_number


class FallbackEnhancedOperations(IntegratedDatabaseOperations):
    """
    Fallback implementation of enhanced operations that works with existing schema.

    This class provides enhanced functionality while gracefully handling missing
    tables and columns by falling back to existing implementations or skipping
    features that require new schema elements.
    """

    def __init__(self, db_connection, db_cursor, branch_id=1, user_id=1, logger=None):
        """Initialize fallback enhanced operations."""
        super().__init__(db_connection, db_cursor, branch_id, user_id, logger)
        self._check_available_features()

    def _check_available_features(self):
        """Check which enhanced features are available based on existing schema."""
        self.enhanced_features = {
            'duplicate_files': self._table_exists('duplicate_files'),
            'processing_logs': self._table_exists('processing_logs'),
            'partial_suffix_tracking': self._table_exists('partial_suffix_tracking'),
            'enhanced_awb_hashes': self._column_exists('awb_hashes', 'content_hash'),
            'processing_status': self._column_exists('master_waybills', 'processing_status'),
            'enhanced_partials': self._column_exists('partial_waybills', 'processing_status')
        }

        # Also set features for backward compatibility
        self.features = self.enhanced_features

        if self.logger:
            self.logger.info(f"Available enhanced features: {self.enhanced_features}")

    def _table_exists(self, table_name: str) -> bool:
        """Check if a table exists."""
        try:
            self.db_cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = %s
                )
            """, (table_name,))
            return self.db_cursor.fetchone()[0]
        except Exception:
            return False

    def _column_exists(self, table_name: str, column_name: str) -> bool:
        """Check if a column exists in a table."""
        try:
            self.db_cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns
                    WHERE table_name = %s
                    AND column_name = %s
                )
            """, (table_name, column_name))
            return self.db_cursor.fetchone()[0]
        except Exception:
            return False

    # ==================== ENHANCED FILE DUPLICATE PREVENTION (FALLBACK) ====================

    def generate_file_hash(self, file_path: str) -> str:
        """Generate SHA-256 hash for entire file content."""
        sha256_hash = hashlib.sha256()
        try:
            with open(file_path, "rb") as f:
                for byte_block in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(byte_block)
            return sha256_hash.hexdigest()
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error generating file hash for {file_path}: {str(e)}")
            raise

    def check_file_duplicate(self, file_hash: str) -> Optional[Dict[str, Any]]:
        """Check if file with given hash already exists (fallback to existing table)."""
        if not self.features['duplicate_files']:
            # Fallback: use existing awb_hashes table if available
            if self._table_exists('awb_hashes'):
                try:
                    # Check what columns exist in awb_hashes
                    if self._column_exists('awb_hashes', 'processed_at'):
                        result = self.find_record(
                            'awb_hashes',
                            'hash = %s',
                            (file_hash,),
                            'awb_number, processed_at'
                        )
                    else:
                        result = self.find_record(
                            'awb_hashes',
                            'hash = %s',
                            (file_hash,),
                            'awb_number'
                        )

                    if result:
                        return {
                            'awb_number': result[0],
                            'first_processed_at': result[1] if len(result) > 1 else datetime.now(),
                            'attempt_count': 1
                        }
                except Exception as e:
                    if self.logger:
                        self.logger.warning(f"Fallback duplicate check failed: {e}")
            return None

        # Use enhanced table if available
        result = self.find_record(
            'duplicate_files',
            'file_hash = %s',
            (file_hash,),
            'id, file_name, first_processed_at, attempt_count'
        )

        if result:
            return {
                'id': result[0],
                'file_name': result[1],
                'first_processed_at': result[2],
                'attempt_count': result[3]
            }
        return None

    def record_file_processed(self, file_path: str, file_hash: str, message_type: str, file_size: int) -> Optional[int]:
        """Record file as processed (fallback implementation)."""
        if not self.features['duplicate_files']:
            if self.logger:
                self.logger.info(f"File duplicate tracking not available, skipping: {os.path.basename(file_path)}")
            return None

        data = {
            'file_name': os.path.basename(file_path),
            'file_hash': file_hash,
            'message_type': message_type.upper(),
            'file_path': file_path,
            'file_size': file_size,
            'first_processed_at': datetime.now(),
            'last_attempted_at': datetime.now(),
            'attempt_count': 1,
            'branch_id': self.branch_id,
            'created_by': self.user_id,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }

        return self.insert_record('duplicate_files', data, 'id')

    def record_duplicate_attempt(self, file_hash: str) -> None:
        """Record a duplicate file attempt (fallback)."""
        if not self.features['duplicate_files']:
            return

        self.execute_query(
            """UPDATE duplicate_files
               SET attempt_count = attempt_count + 1,
                   last_attempted_at = %s
               WHERE file_hash = %s""",
            (datetime.now(), file_hash)
        )

    # ==================== ENHANCED PROCESSING LOG (FALLBACK) ====================

    def start_processing_log(self, file_name: str, message_type: str) -> str:
        """Start a processing log session (fallback)."""
        log_id = f"LOG-{uuid.uuid4()}"

        if not self.features['processing_logs']:
            # Fallback: use existing processing_log table if available
            if self._table_exists('processing_log'):
                data = {
                    'file_name': file_name,
                    'type': message_type.upper(),
                    'success': False,
                    'awb_count': 0,
                    'partial_count': 0,
                    'parsed_at': datetime.now(),
                    'branch_id': self.branch_id
                }
                self.insert_record('processing_log', data)

            if self.logger:
                self.logger.info(f"Enhanced processing logs not available, using fallback for: {file_name}")
            return log_id

        data = {
            'log_id': log_id,
            'file_name': file_name,
            'message_type': message_type.upper(),
            'status': 'STARTED',
            'started_at': datetime.now(),
            'awbs_processed': 0,
            'ulds_processed': 0,
            'errors_count': 0,
            'branch_id': self.branch_id,
            'created_by': self.user_id,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }

        self.insert_record('processing_logs', data)
        return log_id

    def complete_processing_log(self, log_id: str, success: bool, awbs_processed: int = 0,
                               ulds_processed: int = 0, error_message: str = None,
                               processing_summary: Dict[str, Any] = None) -> None:
        """Complete a processing log session (fallback)."""
        if not self.features['processing_logs']:
            if self.logger:
                self.logger.info(f"Processing log completion skipped (enhanced logs not available): {log_id}")
            return

        # Calculate duration
        result = self.find_record(
            'processing_logs',
            'log_id = %s',
            (log_id,),
            'started_at'
        )

        duration_seconds = 0
        if result:
            started_at = result[0]
            duration_seconds = int((datetime.now() - started_at).total_seconds())

        update_data = {
            'status': 'SUCCESS' if success else 'FAILED',
            'completed_at': datetime.now(),
            'duration_seconds': duration_seconds,
            'awbs_processed': awbs_processed,
            'ulds_processed': ulds_processed,
            'error_message': error_message,
            'processing_summary': self.format_json_field(processing_summary),
            'updated_at': datetime.now()
        }

        self.update_record(
            'processing_logs',
            update_data,
            'log_id = %s',
            (log_id,)
        )

    def mark_processing_duplicate(self, log_id: str) -> None:
        """Mark processing log as duplicate (fallback)."""
        if not self.features['processing_logs']:
            return

        update_data = {
            'status': 'DUPLICATE',
            'completed_at': datetime.now(),
            'updated_at': datetime.now()
        }

        self.update_record(
            'processing_logs',
            update_data,
            'log_id = %s',
            (log_id,)
        )

    # ==================== ENHANCED AWB CONTENT DUPLICATE PREVENTION (FALLBACK) ====================

    def generate_awb_content_hash(self, awb_data: Dict[str, Any]) -> str:
        """Generate SHA-256 hash for AWB content (enhanced version)."""
        # Use the existing generate_awb_hash method as fallback
        return self.generate_awb_hash(awb_data)

    def check_awb_content_duplicate(self, awb_number: str, content_hash: str) -> bool:
        """Check if AWB with identical content already exists (fallback)."""
        if self.features['enhanced_awb_hashes']:
            return self.record_exists(
                'awb_hashes',
                'awb_number = %s AND content_hash = %s',
                (awb_number, content_hash)
            )
        else:
            # Fallback to existing hash checking
            return self.check_awb_hash_exists(awb_number, content_hash)

    def record_awb_processed(self, awb_number: str, content_hash: str, manifest_id: str, source_file: str) -> Optional[int]:
        """Record AWB as processed (fallback)."""
        if self.features['enhanced_awb_hashes']:
            data = {
                'awb_number': awb_number,
                'manifest_id': manifest_id,
                'content_hash': content_hash,
                'source_file': source_file,
                'processed_at': datetime.now(),
                'branch_id': self.branch_id,
                'created_by': self.user_id,
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            }
            return self.insert_record('awb_hashes', data, 'id')
        else:
            # Fallback to existing method
            self.store_awb_hash(awb_number, content_hash)
            return None

    # ==================== ENHANCED MASTER WAYBILL OPERATIONS (FALLBACK) ====================

    def create_or_update_master_waybill_enhanced(self, awb_data: Dict[str, Any],
                                                 source_type: str = 'XFFM') -> Tuple[str, bool]:
        awb_data = awb_data.copy()
        awb_data['awb_number'] = normalize_awb_number(awb_data['awb_number'])

        if self.features['processing_status']:
            # Use enhanced logic if processing_status column exists
            return self._create_or_update_with_processing_status(awb_data, source_type)
        else:
            # Fallback to existing method
            return self.create_or_update_master_waybill(awb_data, is_orphan=False)

    def _create_or_update_with_processing_status(self, awb_data: Dict[str, Any],
                                                source_type: str) -> Tuple[str, bool]:
        awb_data = awb_data.copy()
        awb_number = normalize_awb_number(awb_data['awb_number'])
        awb_data['awb_number'] = awb_number

        # Check if AWB already exists
        existing = self.find_record(
            'master_waybills',
            'awb_number = %s',
            (awb_number,),
            'awb_number, processing_status'
        )

        if existing:
            # Update existing with enhanced logic
            update_data = {
                'updated_at': datetime.now(),
                'updated_by': self.user_id
            }

            if source_type == 'XFFM' and existing[1] == 'PENDING':
                update_data['processing_status'] = 'AWAITING_CHECKIN'
            elif source_type == 'XFWB' and existing[1] == 'PENDING':
                update_data['processing_status'] = 'AWAITING_CHECKIN'

            self.update_record(
                'master_waybills',
                update_data,
                'awb_number = %s',
                (awb_number,)
            )
            return awb_number, False
        else:
            # Create new with enhanced logic
            data = awb_data.copy()
            data['awb_number'] = awb_number

            data = {
                'awb_number': awb_number,
                'type_code': awb_data.get('type_code', '740'),
                'manifest_id': awb_data.get('manifest_id'),
                'origin_airport': awb_data.get('origin_airport'),
                'destination_airport': awb_data.get('destination_airport'),
                'total_pieces': awb_data.get('pieces', awb_data.get('total_pieces', 0)),
                'total_weight': awb_data.get('weight', awb_data.get('total_weight', 0)),
                'weight_unit': awb_data.get('weight_unit', 'KGM'),
                'processing_status': 'AWAITING_CHECKIN' if source_type in ['XFFM', 'XFWB'] else 'PENDING',
                'status': 'EXPECTED',
                'branch_id': self.branch_id,
                'created_by': self.user_id,
                'updated_by': self.user_id,
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            }

            self.insert_record('master_waybills', data)
            return awb_number, True
