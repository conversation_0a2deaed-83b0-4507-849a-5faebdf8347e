#!/usr/bin/env python3
"""
XFZB database operations.

This module provides functionality for saving XFZB data to the database.
"""

import os
import sys

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.base_operations import BaseDatabaseOperations


class XFZBDatabaseOperations(BaseDatabaseOperations):
    """
    Database operations for XFZB (House Air Waybill) data.

    This class handles saving XFZB data to the database including
    house waybills, parties, and related information.
    """

    def __init__(self, db_connection, db_cursor, user_id=1, branch_id=1, logger=None):
        """
        Initialize XFZB database operations.

        Args:
            db_connection: Database connection object.
            db_cursor: Database cursor object.
            user_id (int): User ID for audit fields.
            branch_id (int): Branch ID for audit fields.
            logger (logging.Logger): Logger instance.
        """
        super().__init__(db_connection, db_cursor, logger)
        self.user_id = user_id
        self.branch_id = branch_id

    def save_data(self, data):
        """
        Save XFZB data to the database.

        Args:
            data (dict): Extracted and validated XFZB data.

        Returns:
            dict: Result of save operation including IDs and status.
        """
        result = {"success": False, "hawb_id": None, "errors": [], "warnings": []}

        try:
            # Import partial detection service
            import os
            import sys

            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from services.partial_detection_service import PartialDetectionService

            # Initialize partial detection service
            partial_service = PartialDetectionService(self, self.logger)

            # Check for partial AWB detection
            partial_result = partial_service.detect_partial_from_totals_comparison(
                awb_number=data["hawb_number"],
                incoming_pieces=data.get("total_pieces", 0),
                incoming_weight=data.get("total_weight", 0.0),
                source="XFZB",
                manifest_id=data.get("manifest_id"),
                is_house=True,
            )

            # Handle partial detection results
            if partial_result["action"] == "FLAG_REVIEW":
                result["errors"].extend(partial_result["errors"])
                result["warnings"].append(
                    f"House AWB {data['hawb_number']} flagged for manual review due to totals discrepancy"
                )
                return result
            elif partial_result["action"] == "CREATE_PARTIAL":
                result["warnings"].append(
                    f"Created partial waybill {partial_result['partial_id']} for originally received portion"
                )
                # Continue with saving the updated AWB with new totals

            # Check if house AWB already exists (after partial detection)
            existing_hawb = self.find_existing_hawb(data["hawb_number"])
            if existing_hawb and partial_result["action"] != "CREATE_PARTIAL":
                result["errors"].append(
                    f"House AWB {data['hawb_number']} already exists"
                )
                return result

            # Verify master AWB exists
            master_awb = self.find_master_awb(data["mawb_number"])
            if not master_awb:
                result["errors"].append(f"Master AWB {data['mawb_number']} not found")
                return result

            # Save house waybill (new or updated)
            if partial_result["totals_updated"]:
                # House AWB was updated by partial detection, get the ID
                hawb_id = existing_hawb[0] if existing_hawb else None
                result["hawb_id"] = hawb_id
            else:
                # Save new house waybill
                hawb_id = self.save_house_waybill(data, master_awb)
                result["hawb_id"] = hawb_id

            # Save party information
            self.save_party_information(hawb_id, data)

            # Save special handling codes
            self.save_special_handling_codes(hawb_id, data)

            # Commit transaction
            self.commit_transaction()

            result["success"] = True
            self.logger.info(
                f"Successfully saved XFZB data for House AWB {data['hawb_number']}"
            )

        except Exception as e:
            # Rollback transaction on error
            self.rollback_transaction()
            error_msg = f"Error saving XFZB data: {e}"
            result["errors"].append(error_msg)
            self.logger.error(error_msg)

        return result

    def find_existing_hawb(self, hawb_number):
        """
        Check if a house AWB already exists in the database.

        Args:
            hawb_number (str): House AWB number to check.

        Returns:
            tuple: Existing house AWB record or None.
        """
        return self.find_record(
            "house_waybills",
            "hawb_number = %s",
            (hawb_number,),
            "hawb_id, hawb_number, status",
        )

    def find_master_awb(self, mawb_number):
        """
        Find the master AWB record.

        Args:
            mawb_number (str): Master AWB number.

        Returns:
            tuple: Master AWB record or None.
        """
        return self.find_record(
            "master_waybills",
            "awb_number = %s",
            (mawb_number,),
            "awb_id, awb_number, manifest_id, status",
        )

    def save_house_waybill(self, data, master_awb):
        """
        Save house waybill data.

        Args:
            data (dict): XFZB data.
            master_awb (tuple): Master AWB record.

        Returns:
            int: House AWB ID of the saved record.
        """
        # Prepare house waybill data
        hawb_data = {
            "hawb_number": data["hawb_number"],
            "mawb_number": data["mawb_number"],
            # Note: house_waybills table doesn't have type_code column (always '703' in views)
            "manifest_id": master_awb[2],  # Get manifest_id from master AWB
            "origin_airport": data["origin_airport"],
            "destination_airport": data["destination_airport"],
            "total_pieces": data.get("total_pieces", 0),
            "total_weight": data.get("total_weight", 0.0),
            "weight_unit": data.get("weight_unit", "KGM"),
            "special_handling_code": data.get("special_handling_code"),
            "summary_description": data.get(
                "description"
            ),  # Use summary_description instead of description
            "is_mail": data.get("is_mail", False),
            "is_human_remains": data.get("is_human_remains", False),
            "is_partial": data.get("is_partial", False),
            "is_reconciled": False,
            "status": "PENDING",
            "branch_id": self.branch_id,
        }

        # Store complete original XML content for audit trails and reprocessing
        # Following OLD-PARSER format: json.dumps({'xml': xml_string})
        xml_content = data.get("xml_content")
        if xml_content:
            import json

            # Store XML content in OLD-PARSER compatible format
            hawb_data["xml_data"] = json.dumps({"xml": xml_content})
            self.logger.info(
                f"Storing original XML content ({len(xml_content)} characters) for HAWB {data['hawb_number']}"
            )

        # Add timestamps and audit fields
        self.add_timestamps(hawb_data, self.user_id, self.user_id)

        # Insert house waybill
        hawb_id = self.insert_record("house_waybills", hawb_data, "hawb_id")

        self.logger.info(f"Saved house waybill with ID {hawb_id}")
        return hawb_id

    def save_party_information(self, hawb_id, data):
        """
        Save party information (shipper, consignee).

        Args:
            hawb_id (int): House AWB ID.
            data (dict): XFZB data.
        """
        party_ids = {}

        # Save shipper
        shipper_data = data.get("shipper_data")
        if shipper_data:
            party_ids["shipper_code"] = self.find_or_create_shipper(shipper_data)

        # Save consignee
        consignee_data = data.get("consignee_data")
        if consignee_data:
            party_ids["consignee_code"] = self.find_or_create_consignee(consignee_data)

        # Update the house waybill with party IDs
        if party_ids:
            self.update_record("house_waybills", party_ids, "hawb_id = %s", (hawb_id,))
            self.logger.info(
                f"Updated House AWB {hawb_id} with party information: {party_ids}"
            )

        return party_ids

    def find_or_create_shipper(self, shipper_data):
        """
        Find or create a shipper record.

        Args:
            shipper_data (dict): Shipper data.

        Returns:
            int: Shipper ID.
        """
        name = shipper_data.get("name", "").strip()
        if not name:
            return None

        # Try to find existing shipper by name
        existing = self.find_record(
            "shippers", "LOWER(name) = LOWER(%s)", (name,), "id"
        )

        if existing:
            return existing[0]

        # Create new shipper
        shipper_record = {
            "name": name,
            "address_line1": shipper_data.get("address", ""),
            "city": shipper_data.get("city", ""),
            "country_code": shipper_data.get("country_code", ""),
            "postal_code": shipper_data.get("postal_code", ""),
            "is_active": True,
        }

        # Add timestamps (shippers table doesn't have created_by/updated_by columns)
        self.add_timestamps(shipper_record)

        shipper_id = self.insert_record("shippers", shipper_record, "id")
        self.logger.info(f"Created new shipper: {name} (ID: {shipper_id})")
        return shipper_id

    def find_or_create_consignee(self, consignee_data):
        """
        Find or create a consignee record.

        Args:
            consignee_data (dict): Consignee data.

        Returns:
            int: Consignee ID.
        """
        name = consignee_data.get("name", "").strip()
        if not name:
            return None

        # Try to find existing consignee by name
        existing = self.find_record(
            "consignees", "LOWER(name) = LOWER(%s)", (name,), "id"
        )

        if existing:
            return existing[0]

        # Create new consignee
        consignee_record = {
            "name": name,
            "address_line1": consignee_data.get("address", ""),
            "city": consignee_data.get("city", ""),
            "country_code": consignee_data.get("country_code", ""),
            "postal_code": consignee_data.get("postal_code", ""),
            "is_active": True,
        }

        # Add timestamps (consignees table doesn't have created_by/updated_by columns)
        self.add_timestamps(consignee_record)

        consignee_id = self.insert_record("consignees", consignee_record, "id")
        self.logger.info(f"Created new consignee: {name} (ID: {consignee_id})")
        return consignee_id

    def save_special_handling_codes(self, hawb_id, data):
        """
        Save special handling codes for house waybill.

        Args:
            hawb_id (int): House AWB ID.
            data (dict): XFZB data.
        """
        # Save handling codes if any special handling codes exist
        shc_codes = data.get("special_handling_codes", [])
        for code in shc_codes:
            self.save_handling_instruction(hawb_id, code)

    def save_handling_instruction(self, hawb_id, handling_code):
        """
        Save special handling code for house waybill.

        Args:
            hawb_id (int): House AWB ID.
            handling_code (str): Special handling code.
        """
        # Check if the special handling code exists in the master table
        shc_exists = self.find_record(
            "special_handling_codes",
            "code = %s AND is_active = TRUE",
            (handling_code,),
            "id",
        )

        if not shc_exists:
            self.logger.warning(
                f"Special handling code '{handling_code}' not found in master table"
            )
            return None

        # Check if this House AWB already has this SHC
        existing = self.find_record(
            "awb_special_handling_codes",
            "hawb_id = %s AND code = %s AND is_house = TRUE",
            (hawb_id, handling_code),
            "id",
        )

        if existing:
            self.logger.info(
                f"Special handling code {handling_code} already exists for House AWB {hawb_id}"
            )
            return existing[0]

        instruction_data = {
            "hawb_id": hawb_id,
            "code": handling_code,
            "is_house": True,  # This is for house waybill
        }

        # Add timestamps (awb_special_handling_codes table has timestamps but no created_by/updated_by)
        self.add_timestamps(instruction_data)

        # Insert special handling code
        instruction_id = self.insert_record(
            "awb_special_handling_codes", instruction_data, "id"
        )

        self.logger.info(
            f"Saved special handling code {handling_code} with ID {instruction_id}"
        )
        return instruction_id

    def update_hawb_status(self, hawb_number, status, notes=None):
        """
        Update house AWB status.

        Args:
            hawb_number (str): House AWB number.
            status (str): New status.
            notes (str): Optional status notes.

        Returns:
            int: Number of affected rows.
        """
        update_data = {"status": status, "updated_by": self.user_id}

        if notes:
            update_data["status_notes"] = notes

        self.add_timestamps(update_data, updated_by=self.user_id)

        return self.update_record(
            "house_waybills", update_data, "hawb_number = %s", (hawb_number,)
        )

    def get_hawb_summary(self, hawb_number):
        """
        Get house AWB summary information.

        Args:
            hawb_number (str): House AWB number.

        Returns:
            dict: House AWB summary data.
        """
        # Get house waybill data with party IDs
        hawb_data = self.find_record(
            "house_waybills",
            "hawb_number = %s",
            (hawb_number,),
            """hawb_id, hawb_number, mawb_number, type_code, origin_airport,
               destination_airport, total_pieces, total_weight, weight_unit,
               status, created_at, shipper_code, consignee_code""",
        )

        if not hawb_data:
            return None

        summary = {
            "hawb_id": hawb_data[0],
            "hawb_number": hawb_data[1],
            "mawb_number": hawb_data[2],
            "type_code": hawb_data[3],
            "origin_airport": hawb_data[4],
            "destination_airport": hawb_data[5],
            "total_pieces": hawb_data[6],
            "total_weight": hawb_data[7],
            "weight_unit": hawb_data[8],
            "status": hawb_data[9],
            "created_at": hawb_data[10],
        }

        # Get party information from respective tables
        summary["parties"] = {}

        # Get shipper info
        if hawb_data[11]:  # shipper_code
            shipper = self.find_record(
                "shippers", "id = %s", (hawb_data[11],), "name, city, country_code"
            )
            if shipper:
                summary["parties"]["SHIPPER"] = {
                    "name": shipper[0],
                    "city": shipper[1],
                    "country_code": shipper[2],
                }

        # Get consignee info
        if hawb_data[12]:  # consignee_code
            consignee = self.find_record(
                "consignees", "id = %s", (hawb_data[12],), "name, city, country_code"
            )
            if consignee:
                summary["parties"]["CONSIGNEE"] = {
                    "name": consignee[0],
                    "city": consignee[1],
                    "country_code": consignee[2],
                }

        return summary

    def link_to_master_awb(self, hawb_number, mawb_number):
        """
        Link house AWB to a master AWB.

        Args:
            hawb_number (str): House AWB number.
            mawb_number (str): Master AWB number.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Find master AWB
            master_awb = self.find_master_awb(mawb_number)
            if not master_awb:
                self.logger.error(f"Master AWB {mawb_number} not found")
                return False

            # Update house AWB with master AWB info
            update_data = {
                "mawb_number": mawb_number,
                "manifest_id": master_awb[2],
                "updated_by": self.user_id,
            }

            self.add_timestamps(update_data, updated_by=self.user_id)

            rows_affected = self.update_record(
                "house_waybills", update_data, "hawb_number = %s", (hawb_number,)
            )

            if rows_affected > 0:
                self.commit_transaction()
                self.logger.info(
                    f"Linked house AWB {hawb_number} to master AWB {mawb_number}"
                )
                return True
            else:
                self.logger.warning(
                    f"No rows affected when linking house AWB {hawb_number}"
                )
                return False

        except Exception as e:
            self.rollback_transaction()
            self.logger.error(f"Error linking house AWB to master AWB: {e}")
            return False
