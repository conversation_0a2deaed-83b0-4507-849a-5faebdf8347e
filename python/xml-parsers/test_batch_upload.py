#!/usr/bin/env python3
"""
Test script for batch XML upload functionality.
This script tests the batch upload feature by uploading multiple XFWB files.
"""

import requests
import os
import sys
from pathlib import Path
import urllib3

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_batch_upload():
    """Test the batch upload functionality."""

    # Laravel server URL
    base_url = "http://127.0.0.1:8000"
    batch_upload_url = f"{base_url}/simple-xml-import/batch"

    # Create a session to maintain cookies
    session = requests.Session()
    session.verify = False  # Disable SSL verification

    # Directory containing XFWB test files
    test_files_dir = Path("laraval-side/examples/KQ2738")

    if not test_files_dir.exists():
        print(f"❌ Test files directory not found: {test_files_dir}")
        return False

    # Get XFWB files
    xfwb_files = list(test_files_dir.glob("XFWB-*.xml"))

    if not xfwb_files:
        print(f"❌ No XFWB files found in {test_files_dir}")
        return False

    # Limit to first 5 files for testing
    test_files = xfwb_files[:5]
    print(f"📁 Found {len(xfwb_files)} XFWB files, testing with {len(test_files)} files:")
    for file in test_files:
        print(f"   - {file.name}")

    # First, try to login (we'll use a simple approach - check if we can access the page)
    print("\n🔐 Attempting to access the upload page...")
    print(f"🌐 URL: {base_url}/simple-xml-import")
    try:
        response = session.get(f"{base_url}/simple-xml-import")

        # If we get redirected to login, we need to authenticate
        if response.status_code == 302 or 'login' in response.url:
            print("🔑 Authentication required - attempting to login...")

            # Get login page to get CSRF token
            login_response = session.get(f"{base_url}/login")
            if login_response.status_code != 200:
                print(f"❌ Failed to get login page: {login_response.status_code}")
                return False

            # Extract CSRF token from login page
            import re
            csrf_match = re.search(r'<meta name="csrf-token" content="([^"]+)"', login_response.text)
            if not csrf_match:
                print("❌ Could not find CSRF token in login page")
                return False

            csrf_token = csrf_match.group(1)

            # Try to login with a test user (we'll use the admin user)
            login_data = {
                'email': '<EMAIL>',
                'password': 'password',  # Common default password
                '_token': csrf_token
            }

            login_result = session.post(f"{base_url}/login", data=login_data)

            # Try to access the upload page again
            response = session.get(f"{base_url}/simple-xml-import")

        if response.status_code != 200:
            print(f"❌ Failed to access upload page: {response.status_code}")
            print(f"Response URL: {response.url}")
            return False

        # Extract CSRF token from the upload page
        import re
        csrf_match = re.search(r'<meta name="csrf-token" content="([^"]+)"', response.text)
        if not csrf_match:
            print("❌ Could not find CSRF token in upload page")
            return False

        csrf_token = csrf_match.group(1)
        print(f"✅ Successfully accessed upload page and got CSRF token: {csrf_token[:20]}...")

    except Exception as e:
        print(f"❌ Error accessing upload page: {e}")
        return False

    # Prepare files for upload
    print("\n📤 Preparing batch upload...")
    files = []
    data = {
        'xml_type': 'XFWB',
        '_token': csrf_token
    }

    try:
        for file_path in test_files:
            with open(file_path, 'rb') as f:
                files.append(('xml_files[]', (file_path.name, f.read(), 'application/xml')))

        # Send batch upload request
        print(f"🚀 Uploading {len(files)} files...")
        response = session.post(
            batch_upload_url,
            data=data,
            files=files,
            headers={
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': csrf_token
            }
        )

        print(f"📊 Response status: {response.status_code}")

        if response.status_code == 200:
            try:
                result = response.json()
                print("✅ Batch upload completed!")
                print(f"📈 Success: {result.get('success', False)}")
                print(f"📝 Message: {result.get('message', 'No message')}")

                if 'results' in result:
                    print(f"\n📋 Individual file results:")
                    for file_result in result['results']:
                        status = "✅" if file_result.get('success') else "❌"
                        print(f"   {status} {file_result.get('file_name', 'Unknown')}: {file_result.get('message', file_result.get('error', 'No details'))}")

                return result.get('success', False)

            except Exception as e:
                print(f"❌ Error parsing JSON response: {e}")
                print(f"Raw response: {response.text[:500]}...")
                return False
        else:
            print(f"❌ Upload failed with status {response.status_code}")
            print(f"Response: {response.text[:500]}...")
            return False

    except Exception as e:
        print(f"❌ Error during upload: {e}")
        return False

def main():
    """Main function."""
    print("🧪 Testing Batch XML Upload Functionality")
    print("=" * 50)

    success = test_batch_upload()

    print("\n" + "=" * 50)
    if success:
        print("🎉 Batch upload test PASSED!")
    else:
        print("💥 Batch upload test FAILED!")

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
